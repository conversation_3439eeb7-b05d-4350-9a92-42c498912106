2025-08-29 06:46:18,329 - INFO - Found 1 files to process
2025-08-29 06:46:18,330 - INFO - Processing file 1/1: Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-08-29 06:46:18,330 - INFO - Processing Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad: 1.3GB file, estimated memory: 8.0GB, available: 809.3GB
2025-08-29 06:46:18,330 - INFO - [load_data] Start - Memory: 0.53 GB
2025-08-29 06:46:18,330 - INFO - File size: 1.3GB, using chunk_size=50000
2025-08-29 06:46:18,330 - INFO - Loading subset from /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-08-29 06:46:18,331 - INFO - [load_backed] Start - Memory: 0.53 GB
2025-08-29 06:46:19,531 - INFO - Processing 236606 cells in chunks of 50000
2025-08-29 06:46:19,550 - INFO - Keeping 53 perturbations out of 53
2025-08-29 06:46:19,556 - INFO - Processed 50000/236606 cells
2025-08-29 06:46:19,752 - INFO - [load_backed] End - Memory: 0.73 GB (Δ: +0.21 GB)
2025-08-29 06:46:19,752 - INFO - [subset_to_memory] Start - Memory: 0.73 GB
2025-08-29 06:46:19,762 - INFO - High keep ratio 100.0% ≥ 85%; one-shot to_memory() then subset.
2025-08-29 06:46:50,361 - INFO - [subset_to_memory] End - Memory: 4.75 GB (Δ: ***** GB)
2025-08-29 06:46:50,480 - INFO - [load_data] End - Memory: 4.75 GB (Δ: ***** GB)
2025-08-29 06:46:50,480 - INFO - [debug] after load: X=sparse, dtype=float32
2025-08-29 06:46:52,153 - INFO - Validating data integrity for: after_load
2025-08-29 06:46:52,472 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-08-29 06:46:52,473 - INFO - [detect_format] Start - Memory: 4.75 GB
2025-08-29 06:46:52,615 - INFO - [detect_format] End - Memory: 4.75 GB (Δ: +0.00 GB)
2025-08-29 06:46:52,616 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-08-29 06:46:52,616 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-08-29 06:46:52,616 - INFO - [process_count_branch] Start - Memory: 4.75 GB
2025-08-29 06:46:52,616 - INFO - Processing count data branch
2025-08-29 06:46:52,616 - INFO - Validating data integrity for: before_normalization
2025-08-29 06:46:52,933 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-08-29 06:46:52,933 - INFO - [PROCESS] 正在标准化数据到10000总计数...
2025-08-29 06:46:55,113 - INFO - [PROCESS] 标准化完成
2025-08-29 06:46:55,113 - INFO - Validating data integrity for: after_normalization
2025-08-29 06:46:55,433 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-08-29 06:46:55,433 - INFO - start KD
2025-08-29 06:46:55,634 - INFO - [KD] 总细胞: 236,606 | 对照细胞: 9,809 | 待检查扰动基因: 52
2025-08-29 06:46:55,634 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 52 个基因
2025-08-29 06:46:56,154 - INFO - [KD/stage1] 5/52 gene=EP300 cols=1 ctrl_mean=2.134 pert_mean=1.72 ratio=0.806 keep=False (0.10s)
2025-08-29 06:46:56,756 - INFO - [KD/stage1] 10/52 gene=PIK3CA cols=1 ctrl_mean=0.8533 pert_mean=0.57 ratio=0.668 keep=False (0.11s)
2025-08-29 06:46:57,461 - INFO - [KD/stage1] 15/52 gene=HRAS cols=1 ctrl_mean=0.04375 pert_mean=0.0131 ratio=0.299 keep=True (0.20s)
2025-08-29 06:46:58,165 - INFO - [KD/stage1] 20/52 gene=RAF1 cols=1 ctrl_mean=1.271 pert_mean=1.107 ratio=0.871 keep=False (0.11s)
2025-08-29 06:46:58,839 - INFO - [KD/stage1] 25/52 gene=PAK1 cols=1 ctrl_mean=1.392 pert_mean=1.067 ratio=0.767 keep=False (0.16s)
2025-08-29 06:46:59,468 - INFO - [KD/stage1] 30/52 gene=MAPK14 cols=1 ctrl_mean=1.859 pert_mean=0.6866 ratio=0.369 keep=False (0.13s)
2025-08-29 06:47:00,087 - INFO - [KD/stage1] 35/52 gene=PRKCA cols=1 ctrl_mean=11.15 pert_mean=7.214 ratio=0.647 keep=False (0.15s)
2025-08-29 06:47:00,695 - INFO - [KD/stage1] 40/52 gene=SMAD5 cols=1 ctrl_mean=1.211 pert_mean=1.161 ratio=0.958 keep=False (0.10s)
2025-08-29 06:47:01,321 - INFO - [KD/stage1] 45/52 gene=TGFBR2 cols=1 ctrl_mean=1.286 pert_mean=0.4499 ratio=0.350 keep=False (0.15s)
2025-08-29 06:47:01,818 - INFO - [KD/stage1] 50/52 gene=HDAC4 cols=1 ctrl_mean=2.052 pert_mean=0.5113 ratio=0.249 keep=True (0.10s)
2025-08-29 06:47:02,079 - INFO - [KD/stage1] 52/52 gene=TGFBR1 cols=1 ctrl_mean=1.084 pert_mean=0.1751 ratio=0.162 keep=True (0.14s)
2025-08-29 06:47:02,203 - INFO - [KD/stage2] gene=MAPK1 分片 1/1 kept_in_chunk=2267 kept_total=2267 rows 0-2660
2025-08-29 06:47:02,203 - INFO - [KD/stage2] gene=MAPK1 完成，保留细胞 2267/2661（0.11s）
2025-08-29 06:47:02,347 - INFO - [KD/stage2] gene=KRAS 分片 1/1 kept_in_chunk=5449 kept_total=5449 rows 0-5799
2025-08-29 06:47:02,347 - INFO - [KD/stage2] gene=KRAS 完成，保留细胞 5449/5800（0.14s）
2025-08-29 06:47:02,547 - INFO - [KD/stage2] gene=HRAS 分片 1/1 kept_in_chunk=12089 kept_total=12089 rows 0-12165
2025-08-29 06:47:02,547 - INFO - [KD/stage2] gene=HRAS 完成，保留细胞 12089/12166（0.20s）
2025-08-29 06:47:02,749 - INFO - [KD/stage2] gene=SMURF1 分片 1/1 kept_in_chunk=10126 kept_total=10126 rows 0-12093
2025-08-29 06:47:02,750 - INFO - [KD/stage2] gene=SMURF1 完成，保留细胞 10126/12094（0.20s）
2025-08-29 06:47:02,881 - INFO - [KD/stage2] gene=JUN 分片 1/1 kept_in_chunk=4193 kept_total=4193 rows 0-4577
2025-08-29 06:47:02,881 - INFO - [KD/stage2] gene=JUN 完成，保留细胞 4193/4578（0.13s）
2025-08-29 06:47:03,005 - INFO - [KD/stage2] gene=SMAD1 分片 1/1 kept_in_chunk=3684 kept_total=3684 rows 0-3795
2025-08-29 06:47:03,005 - INFO - [KD/stage2] gene=SMAD1 完成，保留细胞 3684/3796（0.12s）
2025-08-29 06:47:03,143 - INFO - [KD/stage2] gene=RUNX3 分片 1/1 kept_in_chunk=5285 kept_total=5285 rows 0-5323
2025-08-29 06:47:03,143 - INFO - [KD/stage2] gene=RUNX3 完成，保留细胞 5285/5324（0.14s）
2025-08-29 06:47:03,286 - INFO - [KD/stage2] gene=SMAD9 分片 1/1 kept_in_chunk=5809 kept_total=5809 rows 0-5866
2025-08-29 06:47:03,286 - INFO - [KD/stage2] gene=SMAD9 完成，保留细胞 5809/5867（0.14s）
2025-08-29 06:47:03,386 - INFO - [KD/stage2] gene=RHOA 分片 1/1 kept_in_chunk=1023 kept_total=1023 rows 0-1139
2025-08-29 06:47:03,386 - INFO - [KD/stage2] gene=RHOA 完成，保留细胞 1023/1140（0.10s）
2025-08-29 06:47:03,489 - INFO - [KD/stage2] gene=HDAC4 分片 1/1 kept_in_chunk=1255 kept_total=1255 rows 0-1452
2025-08-29 06:47:03,489 - INFO - [KD/stage2] gene=HDAC4 完成，保留细胞 1255/1453（0.10s）
2025-08-29 06:47:03,635 - INFO - [KD/stage2] gene=TGFBR1 分片 1/1 kept_in_chunk=5542 kept_total=5542 rows 0-5905
2025-08-29 06:47:03,636 - INFO - [KD/stage2] gene=TGFBR1 完成，保留细胞 5542/5906（0.15s）
2025-08-29 06:47:03,667 - INFO - [KD] 完成：保留 66,531/236,606 细胞 | 对照 9,809 | 通过基因 11 | 用时 8.03s
2025-08-29 06:47:03,840 - INFO - [FIX] materialize KD result (copy) to avoid sparse view assignment
2025-08-29 06:47:04,519 - INFO - Validating data integrity for: after_filter_count
2025-08-29 06:47:04,606 - INFO - Data validation passed: 66531 cells, 18080 genes
2025-08-29 06:47:04,606 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-08-29 06:47:04,937 - INFO - [PROCESS] log1p 转换完成
2025-08-29 06:47:05,073 - INFO - [process_count_branch] End - Memory: 5.89 GB (Δ: ***** GB)
2025-08-29 06:47:05,073 - INFO - [standardize] Start - Memory: 5.89 GB
2025-08-29 06:47:05,073 - INFO - [standardize_names] Start - Memory: 5.89 GB
2025-08-29 06:47:05,073 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-08-29 06:47:05,073 - INFO - Control label: non-targeting -> non-targeting
2025-08-29 06:47:05,073 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-08-29 06:47:05,290 - INFO - [standardize_names] End - Memory: 5.89 GB (Δ: +0.00 GB)
2025-08-29 06:47:05,404 - INFO - [standardize] End - Memory: 5.89 GB (Δ: +0.00 GB)
2025-08-29 06:47:05,404 - INFO - Validating data integrity for: final_validation
2025-08-29 06:47:05,491 - INFO - Data validation passed: 66531 cells, 18080 genes
2025-08-29 06:47:05,494 - INFO - [write_output] Start - Memory: 5.89 GB
2025-08-29 06:47:05,494 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-08-29 06:47:05,494 - INFO - [WRITE] 最终数据维度: 66531 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-08-29 06:47:09,289 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-08-29 06:47:09,416 - INFO - [write_output] End - Memory: 5.89 GB (Δ: -0.00 GB)
2025-08-29 06:47:09,417 - INFO - Successfully processed Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-08-29 06:47:09,417 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-08-29 06:47:09,417 - INFO - Cells kept: 66531
2025-08-29 06:47:09,417 - INFO - Genes kept: 18080
2025-08-29 06:47:09,573 - INFO - ✓ Successfully processed Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-08-29 06:47:09,573 - INFO - ============================================================
2025-08-29 06:47:09,573 - INFO - PROCESSING SUMMARY
2025-08-29 06:47:09,573 - INFO - ============================================================
2025-08-29 06:47:09,573 - INFO - Total files: 1
2025-08-29 06:47:09,573 - INFO - Successful: 1
2025-08-29 06:47:09,573 - INFO - Failed: 0
2025-08-29 06:47:09,573 - INFO - ✓ 1 files processed successfully
2025-08-29 06:47:09,573 - INFO - 🎉 All files processed successfully!
