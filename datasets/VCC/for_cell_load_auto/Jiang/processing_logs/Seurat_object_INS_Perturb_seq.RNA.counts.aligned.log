2025-09-01 17:39:13,742 - INFO - Found 1 files to process
2025-09-01 17:39:13,742 - INFO - Processing file 1/1: Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-01 17:39:13,742 - INFO - Processing Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad: 2.5GB file, estimated memory: 8.9GB, available: 930.5GB
2025-09-01 17:39:13,745 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-01 17:39:13,745 - INFO - Memory-efficient environment configured
2025-09-01 17:39:13,745 - INFO - Loading 2.5GB file with chunk_size=200000
2025-09-01 17:39:13,745 - INFO - Available memory: 930.5GB
2025-09-01 17:39:15,972 - INFO - Total observations: 431,457
2025-09-01 17:39:15,973 - INFO - Counting perturbations...
2025-09-01 17:39:16,031 - INFO - Keeping 45 perturbations out of 45
2025-09-01 17:39:16,031 - INFO - Collecting cell indices...
2025-09-01 17:39:16,107 - INFO - Keep statistics: 431,457/431,457 cells (100.0%)
2025-09-01 17:39:16,107 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-01 17:39:16,107 - INFO - Attempting careful full load...
2025-09-01 17:39:16,107 - INFO - Step 1: Loading to memory...
2025-09-01 17:39:48,616 - INFO - Step 2: Converting sparse matrix...
2025-09-01 17:39:48,619 - INFO - Step 3: Creating subset...
2025-09-01 17:39:49,019 - INFO - Creating subset with 431,457 cells using batched approach
2025-09-01 17:39:52,764 - INFO - Created 5 subset chunks
2025-09-01 17:39:56,357 - INFO - Combining 9 chunks...
2025-09-01 17:39:59,776 - INFO - [load_data] End - Memory: 9.14 GB (Δ: ***** GB)
2025-09-01 17:39:59,776 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-01 17:40:03,045 - INFO - Validating data integrity for: after_load
2025-09-01 17:40:03,697 - INFO - Data validation passed: 431457 cells, 18080 genes
2025-09-01 17:40:03,697 - INFO - [detect_format] Start - Memory: 9.14 GB
2025-09-01 17:40:03,809 - INFO - [detect_format] End - Memory: 9.14 GB (Δ: +0.00 GB)
2025-09-01 17:40:03,809 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-01 17:40:03,809 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-01 17:40:03,809 - INFO - [process_count_branch] Start - Memory: 9.14 GB
2025-09-01 17:40:03,809 - INFO - Processing count data branch
2025-09-01 17:40:03,809 - INFO - Validating data integrity for: before_normalization
2025-09-01 17:40:04,465 - INFO - Data validation passed: 431457 cells, 18080 genes
2025-09-01 17:40:04,465 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-01 17:40:04,465 - INFO - [PROCESS] 注意：将为KD检测创建临时归一化副本以提高检测敏感性
2025-09-01 17:40:04,465 - INFO - [PROCESS] 创建临时归一化副本用于KD检测...
2025-09-01 17:40:11,772 - INFO - [PROCESS] 临时归一化完成，将用于KD检测
2025-09-01 17:40:11,772 - INFO - Validating data integrity for: after_normalization
2025-09-01 17:40:12,430 - INFO - Data validation passed: 431457 cells, 18080 genes
2025-09-01 17:40:12,431 - INFO - start KD
2025-09-01 17:40:12,444 - WARNING - Gene name column 'gene_name' not found, using var.index for gene matching
2025-09-01 17:40:12,471 - INFO - [KD] 总细胞: 431,457 | 对照细胞: 26,001 | 待检查扰动基因: 44
2025-09-01 17:40:12,474 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-01 17:40:12,475 - INFO - [DEBUG] - Value range: [0.000000, 85.301834]
2025-09-01 17:40:12,477 - INFO - [DEBUG] - Mean: 0.432956, Std: 1.836451
2025-09-01 17:40:12,481 - INFO - [DEBUG] - Sparsity: 85395/1000000 (8.5% non-zero)
2025-09-01 17:40:12,481 - INFO - [DEBUG] - Skip normalization: True
2025-09-01 17:40:12,482 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-01 17:40:12,482 - INFO - [DEBUG] - Control range: [0.000000, 28.344671]
2025-09-01 17:40:12,482 - INFO - [DEBUG] - Control mean: 0.363683
2025-09-01 17:40:12,482 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 44 个基因
2025-09-01 17:40:12,736 - INFO - [DEBUG] Gene EIF2B1: found cols.size=1, ctrl_mean=0.439885, threshold=-1e-06
2025-09-01 17:40:12,737 - INFO - [DEBUG] Gene EIF2B1: sample ctrl values - min=0.000000, max=9.242145, mean=0.432386, nonzero=13/100
2025-09-01 17:40:13,094 - INFO - [DEBUG] Gene MTOR: found cols.size=1, ctrl_mean=1.973445, threshold=-1e-06
2025-09-01 17:40:13,095 - INFO - [DEBUG] Gene MTOR: sample ctrl values - min=0.000000, max=13.449899, mean=1.676124, nonzero=37/100
2025-09-01 17:40:13,387 - INFO - [DEBUG] Gene PTEN: found cols.size=1, ctrl_mean=2.433881, threshold=-1e-06
2025-09-01 17:40:13,388 - INFO - [DEBUG] Gene PTEN: sample ctrl values - min=0.000000, max=17.079420, mean=3.122075, nonzero=57/100
2025-09-01 17:40:13,816 - INFO - [DEBUG] Gene FOXO3: found cols.size=1, ctrl_mean=2.837990, threshold=-1e-06
2025-09-01 17:40:13,817 - INFO - [DEBUG] Gene FOXO3: sample ctrl values - min=0.000000, max=9.053870, mean=2.206640, nonzero=45/100
2025-09-01 17:40:14,179 - INFO - [DEBUG] Gene SREBF1: found cols.size=1, ctrl_mean=0.692050, threshold=-1e-06
2025-09-01 17:40:14,180 - INFO - [DEBUG] Gene SREBF1: sample ctrl values - min=0.000000, max=12.150668, mean=0.429155, nonzero=11/100
2025-09-01 17:40:14,659 - INFO - [DEBUG/stage1] 7/44 gene=IGF2 → REJECTED: cols.size=0, ctrl_mean=0.000000 <= -1e-06
2025-09-01 17:40:15,658 - INFO - [KD/stage1] 10/44 gene=SHC1 cols=1 ctrl_mean=0.483 pert_mean=0.2691 ratio=0.557 keep=False (0.32s)
2025-09-01 17:40:18,945 - INFO - [KD/stage1] 20/44 gene=RPS6KB1 → 跳过（无有效组件或ctrl_mean<=-1e-06）
2025-09-01 17:40:22,592 - INFO - [KD/stage1] 30/44 gene=TTF1 cols=1 ctrl_mean=0.6049 pert_mean=0.5948 ratio=0.983 keep=False (0.31s)
2025-09-01 17:40:25,930 - INFO - [KD/stage1] 40/44 gene=SRF cols=1 ctrl_mean=0.4207 pert_mean=0.1948 ratio=0.463 keep=False (0.31s)
2025-09-01 17:40:27,113 - INFO - [KD/stage1] 44/44 gene=RAD51 cols=1 ctrl_mean=0.5135 pert_mean=0.4507 ratio=0.878 keep=False (0.28s)
2025-09-01 17:40:27,569 - INFO - [KD/stage2] gene=PTEN 分片 1/1 kept_in_chunk=14150 kept_total=14150 rows 0-16549
2025-09-01 17:40:27,569 - INFO - [KD/stage2] gene=PTEN 完成，保留细胞 14150/16550（0.44s）
2025-09-01 17:40:27,869 - INFO - [KD/stage2] gene=TSC1 分片 1/1 kept_in_chunk=3292 kept_total=3292 rows 0-3613
2025-09-01 17:40:27,869 - INFO - [KD/stage2] gene=TSC1 完成，保留细胞 3292/3614（0.30s）
2025-09-01 17:40:28,330 - INFO - [KD/stage2] gene=IRS2 分片 1/1 kept_in_chunk=17956 kept_total=17956 rows 0-18691
2025-09-01 17:40:28,330 - INFO - [KD/stage2] gene=IRS2 完成，保留细胞 17956/18692（0.46s）
2025-09-01 17:40:28,651 - INFO - [KD/stage2] gene=MAPK1 分片 1/1 kept_in_chunk=5280 kept_total=5280 rows 0-6206
2025-09-01 17:40:28,651 - INFO - [KD/stage2] gene=MAPK1 完成，保留细胞 5280/6207（0.32s）
2025-09-01 17:40:29,031 - INFO - [KD/stage2] gene=SGK1 分片 1/1 kept_in_chunk=10615 kept_total=10615 rows 0-11210
2025-09-01 17:40:29,031 - INFO - [KD/stage2] gene=SGK1 完成，保留细胞 10615/11211（0.38s）
2025-09-01 17:40:29,467 - INFO - [KD/stage2] gene=GRB10 分片 1/1 kept_in_chunk=14169 kept_total=14169 rows 0-16496
2025-09-01 17:40:29,467 - INFO - [KD/stage2] gene=GRB10 完成，保留细胞 14169/16497（0.44s）
2025-09-01 17:40:29,887 - INFO - [KD/stage2] gene=MAPK3 分片 1/1 kept_in_chunk=15011 kept_total=15011 rows 0-15184
2025-09-01 17:40:29,887 - INFO - [KD/stage2] gene=MAPK3 完成，保留细胞 15011/15185（0.42s）
2025-09-01 17:40:30,357 - INFO - [KD/stage2] gene=SREBF2 分片 1/1 kept_in_chunk=16701 kept_total=16701 rows 0-19618
2025-09-01 17:40:30,358 - INFO - [KD/stage2] gene=SREBF2 完成，保留细胞 16701/19619（0.47s）
2025-09-01 17:40:30,687 - INFO - [KD/stage2] gene=CHUK 分片 1/1 kept_in_chunk=6058 kept_total=6058 rows 0-6488
2025-09-01 17:40:30,687 - INFO - [KD/stage2] gene=CHUK 完成，保留细胞 6058/6489（0.33s）
2025-09-01 17:40:30,742 - INFO - [KD] 完成：保留 129,233/431,457 细胞 | 对照 26,001 | 通过基因 9 | 用时 18.26s
2025-09-01 17:40:30,784 - INFO - [PROCESS] 将KD过滤结果应用到原始未归一化数据
2025-09-01 17:40:32,423 - INFO - [PROCESS] 应用过滤结果：保留 129233 个细胞
2025-09-01 17:40:32,611 - INFO - Validating data integrity for: after_filter_count
2025-09-01 17:40:32,806 - INFO - Data validation passed: 129233 cells, 18080 genes
2025-09-01 17:40:32,806 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-01 17:40:33,526 - INFO - [PROCESS] log1p 转换完成
2025-09-01 17:40:33,620 - INFO - [process_count_branch] End - Memory: 11.66 GB (Δ: ***** GB)
2025-09-01 17:40:33,620 - INFO - [standardize] Start - Memory: 11.66 GB
2025-09-01 17:40:33,620 - INFO - [standardize_names] Start - Memory: 11.66 GB
2025-09-01 17:40:33,620 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-01 17:40:33,620 - INFO - Control label: non-targeting -> non-targeting
2025-09-01 17:40:33,620 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-01 17:40:33,624 - WARNING - Gene name column 'gene_name' not found, using index
2025-09-01 17:40:33,904 - INFO - [standardize_names] End - Memory: 11.66 GB (Δ: +0.00 GB)
2025-09-01 17:40:33,984 - INFO - [standardize] End - Memory: 11.66 GB (Δ: +0.00 GB)
2025-09-01 17:40:33,984 - INFO - Validating data integrity for: final_validation
2025-09-01 17:40:34,177 - INFO - Data validation passed: 129233 cells, 18080 genes
2025-09-01 17:40:34,182 - INFO - [write_output] Start - Memory: 11.66 GB
2025-09-01 17:40:34,182 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_INS_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-01 17:40:34,182 - INFO - [WRITE] 最终数据维度: 129233 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'RNA_snn_res.0.9' as categorical
... storing 'seurat_clusters' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-01 17:40:56,826 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_INS_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-01 17:40:56,927 - INFO - [write_output] End - Memory: 11.65 GB (Δ: -0.01 GB)
2025-09-01 17:40:56,927 - INFO - Successfully processed Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-01 17:40:56,927 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_INS_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-01 17:40:56,927 - INFO - Cells kept: 129233
2025-09-01 17:40:56,927 - INFO - Genes kept: 18080
2025-09-01 17:40:57,039 - INFO - ✓ Successfully processed Seurat_object_INS_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-01 17:40:57,039 - INFO - ============================================================
2025-09-01 17:40:57,039 - INFO - PROCESSING SUMMARY
2025-09-01 17:40:57,039 - INFO - ============================================================
2025-09-01 17:40:57,039 - INFO - Total files: 1
2025-09-01 17:40:57,039 - INFO - Successful: 1
2025-09-01 17:40:57,039 - INFO - Failed: 0
2025-09-01 17:40:57,039 - INFO - ✓ 1 files processed successfully
2025-09-01 17:40:57,039 - INFO - 🎉 All files processed successfully!
