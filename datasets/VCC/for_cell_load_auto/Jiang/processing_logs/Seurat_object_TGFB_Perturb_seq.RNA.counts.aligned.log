2025-09-01 17:46:50,293 - INFO - Found 1 files to process
2025-09-01 17:46:50,293 - INFO - Processing file 1/1: Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-01 17:46:50,294 - INFO - Processing Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad: 1.3GB file, estimated memory: 8.0GB, available: 921.5GB
2025-09-01 17:46:50,295 - INFO - [load_data] Start - Memory: 0.24 GB
2025-09-01 17:46:50,295 - INFO - Memory-efficient environment configured
2025-09-01 17:46:50,295 - INFO - Loading 1.3GB file with chunk_size=200000
2025-09-01 17:46:50,295 - INFO - Available memory: 921.4GB
2025-09-01 17:46:51,381 - INFO - Total observations: 236,606
2025-09-01 17:46:51,381 - INFO - Counting perturbations...
2025-09-01 17:46:51,462 - INFO - Keeping 53 perturbations out of 53
2025-09-01 17:46:51,462 - INFO - Collecting cell indices...
2025-09-01 17:46:51,504 - INFO - Keep statistics: 236,606/236,606 cells (100.0%)
2025-09-01 17:46:51,504 - INFO - High keep ratio and sufficient memory, attempting full load
2025-09-01 17:46:51,504 - INFO - Attempting careful full load...
2025-09-01 17:46:51,504 - INFO - Step 1: Loading to memory...
2025-09-01 17:47:06,707 - INFO - Step 2: Converting sparse matrix...
2025-09-01 17:47:06,708 - INFO - Step 3: Creating subset...
2025-09-01 17:47:06,772 - INFO - Creating subset with 236,606 cells using batched approach
2025-09-01 17:47:09,346 - INFO - Created 5 subset chunks
2025-09-01 17:47:09,402 - INFO - Combining 5 chunks...
2025-09-01 17:47:11,110 - INFO - [load_data] End - Memory: 4.49 GB (Δ: ***** GB)
2025-09-01 17:47:11,110 - INFO - [debug] after load: X=sparse, dtype=float32
2025-09-01 17:47:12,683 - INFO - Validating data integrity for: after_load
2025-09-01 17:47:13,003 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-09-01 17:47:13,003 - INFO - [detect_format] Start - Memory: 4.49 GB
2025-09-01 17:47:13,099 - INFO - [detect_format] End - Memory: 4.49 GB (Δ: +0.00 GB)
2025-09-01 17:47:13,100 - INFO - [detect_format] detected is_log1p=False, frac_1e4_like=0.000
2025-09-01 17:47:13,100 - INFO - [PROCESS] 检测为计数数据，使用标准化+log1p分支处理
2025-09-01 17:47:13,100 - INFO - [process_count_branch] Start - Memory: 4.49 GB
2025-09-01 17:47:13,100 - INFO - Processing count data branch
2025-09-01 17:47:13,100 - INFO - Validating data integrity for: before_normalization
2025-09-01 17:47:13,420 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-09-01 17:47:13,420 - INFO - [PROCESS] 跳过归一化步骤，直接使用原始count数据
2025-09-01 17:47:13,420 - INFO - [PROCESS] 注意：将为KD检测创建临时归一化副本以提高检测敏感性
2025-09-01 17:47:13,420 - INFO - [PROCESS] 创建临时归一化副本用于KD检测...
2025-09-01 17:47:16,932 - INFO - [PROCESS] 临时归一化完成，将用于KD检测
2025-09-01 17:47:16,932 - INFO - Validating data integrity for: after_normalization
2025-09-01 17:47:17,250 - INFO - Data validation passed: 236606 cells, 18080 genes
2025-09-01 17:47:17,250 - INFO - start KD
2025-09-01 17:47:17,258 - WARNING - Gene name column 'gene_name' not found, using var.index for gene matching
2025-09-01 17:47:17,278 - INFO - [KD] 总细胞: 236,606 | 对照细胞: 9,809 | 待检查扰动基因: 52
2025-09-01 17:47:17,282 - INFO - [DEBUG] Data characteristics (sample (1000, 1000)):
2025-09-01 17:47:17,283 - INFO - [DEBUG] - Value range: [0.000000, 68.163589]
2025-09-01 17:47:17,286 - INFO - [DEBUG] - Mean: 0.398200, Std: 1.538632
2025-09-01 17:47:17,290 - INFO - [DEBUG] - Sparsity: 91516/1000000 (9.2% non-zero)
2025-09-01 17:47:17,290 - INFO - [DEBUG] - Skip normalization: True
2025-09-01 17:47:17,290 - INFO - [DEBUG] Control group sample ((100, 100)):
2025-09-01 17:47:17,290 - INFO - [DEBUG] - Control range: [0.000000, 22.909508]
2025-09-01 17:47:17,291 - INFO - [DEBUG] - Control mean: 0.267930
2025-09-01 17:47:17,291 - INFO - [KD] Stage 1 - 开始基因级KD判定，共 52 个基因
2025-09-01 17:47:17,379 - INFO - [DEBUG] Gene RUNX2: found cols.size=1, ctrl_mean=1.726093, threshold=-1e-06
2025-09-01 17:47:17,380 - INFO - [DEBUG] Gene RUNX2: sample ctrl values - min=0.000000, max=21.197668, mean=3.603087, nonzero=43/100
2025-09-01 17:47:17,411 - INFO - [DEBUG] Gene RPS6KB1: found cols.size=0, ctrl_mean=0.000000, threshold=-1e-06
2025-09-01 17:47:17,412 - INFO - [DEBUG/stage1] 2/52 gene=RPS6KB1 → REJECTED: cols.size=0, ctrl_mean=0.000000 <= -1e-06
2025-09-01 17:47:17,498 - INFO - [DEBUG] Gene PPP2CA: found cols.size=1, ctrl_mean=1.197147, threshold=-1e-06
2025-09-01 17:47:17,499 - INFO - [DEBUG] Gene PPP2CA: sample ctrl values - min=0.000000, max=9.652510, mean=0.816323, nonzero=18/100
2025-09-01 17:47:17,611 - INFO - [DEBUG] Gene RUNX1: found cols.size=1, ctrl_mean=5.043503, threshold=-1e-06
2025-09-01 17:47:17,612 - INFO - [DEBUG] Gene RUNX1: sample ctrl values - min=0.000000, max=53.333336, mean=7.926036, nonzero=74/100
2025-09-01 17:47:17,806 - INFO - [DEBUG] Gene EP300: found cols.size=1, ctrl_mean=2.134181, threshold=-1e-06
2025-09-01 17:47:17,807 - INFO - [DEBUG] Gene EP300: sample ctrl values - min=0.000000, max=15.552099, mean=1.559689, nonzero=29/100
2025-09-01 17:47:18,447 - INFO - [KD/stage1] 10/52 gene=PIK3CA cols=1 ctrl_mean=0.8533 pert_mean=0.57 ratio=0.668 keep=False (0.12s)
2025-09-01 17:47:19,887 - INFO - [KD/stage1] 20/52 gene=RAF1 cols=1 ctrl_mean=1.271 pert_mean=1.107 ratio=0.871 keep=False (0.11s)
2025-09-01 17:47:21,223 - INFO - [KD/stage1] 30/52 gene=MAPK14 cols=1 ctrl_mean=1.859 pert_mean=0.6866 ratio=0.369 keep=False (0.13s)
2025-09-01 17:47:22,489 - INFO - [KD/stage1] 40/52 gene=SMAD5 cols=1 ctrl_mean=1.211 pert_mean=1.161 ratio=0.958 keep=False (0.11s)
2025-09-01 17:47:23,658 - INFO - [KD/stage1] 50/52 gene=HDAC4 cols=1 ctrl_mean=2.052 pert_mean=0.5113 ratio=0.249 keep=True (0.11s)
2025-09-01 17:47:23,955 - INFO - [KD/stage1] 52/52 gene=TGFBR1 cols=1 ctrl_mean=1.084 pert_mean=0.1751 ratio=0.162 keep=True (0.15s)
2025-09-01 17:47:24,081 - INFO - [KD/stage2] gene=MAPK1 分片 1/1 kept_in_chunk=2267 kept_total=2267 rows 0-2660
2025-09-01 17:47:24,081 - INFO - [KD/stage2] gene=MAPK1 完成，保留细胞 2267/2661（0.12s）
2025-09-01 17:47:24,230 - INFO - [KD/stage2] gene=KRAS 分片 1/1 kept_in_chunk=5449 kept_total=5449 rows 0-5799
2025-09-01 17:47:24,230 - INFO - [KD/stage2] gene=KRAS 完成，保留细胞 5449/5800（0.15s）
2025-09-01 17:47:24,435 - INFO - [KD/stage2] gene=HRAS 分片 1/1 kept_in_chunk=12089 kept_total=12089 rows 0-12165
2025-09-01 17:47:24,435 - INFO - [KD/stage2] gene=HRAS 完成，保留细胞 12089/12166（0.21s）
2025-09-01 17:47:24,646 - INFO - [KD/stage2] gene=SMURF1 分片 1/1 kept_in_chunk=10126 kept_total=10126 rows 0-12093
2025-09-01 17:47:24,646 - INFO - [KD/stage2] gene=SMURF1 完成，保留细胞 10126/12094（0.21s）
2025-09-01 17:47:24,783 - INFO - [KD/stage2] gene=JUN 分片 1/1 kept_in_chunk=4193 kept_total=4193 rows 0-4577
2025-09-01 17:47:24,783 - INFO - [KD/stage2] gene=JUN 完成，保留细胞 4193/4578（0.14s）
2025-09-01 17:47:24,913 - INFO - [KD/stage2] gene=SMAD1 分片 1/1 kept_in_chunk=3684 kept_total=3684 rows 0-3795
2025-09-01 17:47:24,913 - INFO - [KD/stage2] gene=SMAD1 完成，保留细胞 3684/3796（0.13s）
2025-09-01 17:47:25,056 - INFO - [KD/stage2] gene=RUNX3 分片 1/1 kept_in_chunk=5285 kept_total=5285 rows 0-5323
2025-09-01 17:47:25,056 - INFO - [KD/stage2] gene=RUNX3 完成，保留细胞 5285/5324（0.14s）
2025-09-01 17:47:25,204 - INFO - [KD/stage2] gene=SMAD9 分片 1/1 kept_in_chunk=5809 kept_total=5809 rows 0-5866
2025-09-01 17:47:25,205 - INFO - [KD/stage2] gene=SMAD9 完成，保留细胞 5809/5867（0.15s）
2025-09-01 17:47:25,307 - INFO - [KD/stage2] gene=RHOA 分片 1/1 kept_in_chunk=1023 kept_total=1023 rows 0-1139
2025-09-01 17:47:25,308 - INFO - [KD/stage2] gene=RHOA 完成，保留细胞 1023/1140（0.10s）
2025-09-01 17:47:25,417 - INFO - [KD/stage2] gene=HDAC4 分片 1/1 kept_in_chunk=1255 kept_total=1255 rows 0-1452
2025-09-01 17:47:25,417 - INFO - [KD/stage2] gene=HDAC4 完成，保留细胞 1255/1453（0.11s）
2025-09-01 17:47:25,570 - INFO - [KD/stage2] gene=TGFBR1 分片 1/1 kept_in_chunk=5542 kept_total=5542 rows 0-5905
2025-09-01 17:47:25,570 - INFO - [KD/stage2] gene=TGFBR1 完成，保留细胞 5542/5906（0.15s）
2025-09-01 17:47:25,604 - INFO - [KD] 完成：保留 66,531/236,606 细胞 | 对照 9,809 | 通过基因 11 | 用时 8.31s
2025-09-01 17:47:25,625 - INFO - [PROCESS] 将KD过滤结果应用到原始未归一化数据
2025-09-01 17:47:26,388 - INFO - [PROCESS] 应用过滤结果：保留 66531 个细胞
2025-09-01 17:47:26,530 - INFO - Validating data integrity for: after_filter_count
2025-09-01 17:47:26,620 - INFO - Data validation passed: 66531 cells, 18080 genes
2025-09-01 17:47:26,620 - INFO - [PROCESS] 正在执行 log1p 转换...
2025-09-01 17:47:26,954 - INFO - [PROCESS] log1p 转换完成
2025-09-01 17:47:27,064 - INFO - [process_count_branch] End - Memory: 5.63 GB (Δ: ***** GB)
2025-09-01 17:47:27,064 - INFO - [standardize] Start - Memory: 5.63 GB
2025-09-01 17:47:27,065 - INFO - [standardize_names] Start - Memory: 5.63 GB
2025-09-01 17:47:27,065 - INFO - Standardizing: pert_col=gene, gene_col=gene_name
2025-09-01 17:47:27,065 - INFO - Control label: non-targeting -> non-targeting
2025-09-01 17:47:27,065 - INFO - Perturbation column type: <class 'pandas.core.series.Series'>, dtype: object
2025-09-01 17:47:27,067 - WARNING - Gene name column 'gene_name' not found, using index
2025-09-01 17:47:27,248 - INFO - [standardize_names] End - Memory: 5.63 GB (Δ: +0.00 GB)
2025-09-01 17:47:27,333 - INFO - [standardize] End - Memory: 5.63 GB (Δ: +0.00 GB)
2025-09-01 17:47:27,333 - INFO - Validating data integrity for: final_validation
2025-09-01 17:47:27,422 - INFO - Data validation passed: 66531 cells, 18080 genes
2025-09-01 17:47:27,424 - INFO - [write_output] Start - Memory: 5.63 GB
2025-09-01 17:47:27,424 - INFO - [WRITE] 开始写入输出文件: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-01 17:47:27,425 - INFO - [WRITE] 最终数据维度: 66531 细胞 × 18080 基因
... storing 'orig.ident' as categorical
... storing 'sample' as categorical
... storing 'cell_type' as categorical
... storing 'pathway' as categorical
... storing 'sample_ID' as categorical
... storing 'Batch_info' as categorical
... storing 'bc1_well' as categorical
... storing 'bc2_well' as categorical
... storing 'bc3_well' as categorical
... storing 'guide' as categorical
... storing 'gene' as categorical
2025-09-01 17:47:38,239 - INFO - Successfully wrote /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-01 17:47:38,336 - INFO - [write_output] End - Memory: 5.63 GB (Δ: -0.00 GB)
2025-09-01 17:47:38,336 - INFO - Successfully processed Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-01 17:47:38,336 - INFO - Output: /data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/compass_cellload_processed/Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.cellload.filtered.h5ad
2025-09-01 17:47:38,336 - INFO - Cells kept: 66531
2025-09-01 17:47:38,336 - INFO - Genes kept: 18080
2025-09-01 17:47:38,425 - INFO - ✓ Successfully processed Seurat_object_TGFB_Perturb_seq.RNA.counts.aligned.h5ad
2025-09-01 17:47:38,425 - INFO - ============================================================
2025-09-01 17:47:38,425 - INFO - PROCESSING SUMMARY
2025-09-01 17:47:38,425 - INFO - ============================================================
2025-09-01 17:47:38,425 - INFO - Total files: 1
2025-09-01 17:47:38,425 - INFO - Successful: 1
2025-09-01 17:47:38,426 - INFO - Failed: 0
2025-09-01 17:47:38,426 - INFO - ✓ 1 files processed successfully
2025-09-01 17:47:38,426 - INFO - 🎉 All files processed successfully!
