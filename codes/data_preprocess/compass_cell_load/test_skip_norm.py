#!/usr/bin/env python3
"""
测试跳过归一化功能的脚本
"""

import os
import sys
import logging
from pathlib import Path

# 设置环境变量以启用调试
os.environ["DEBUG_KD"] = "1"
os.environ["PROGRESS"] = "1"

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from cellload_auto import process_one_file_optimized

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_skip_normalization():
    """测试跳过归一化功能"""
    
    # 测试文件路径
    test_file = Path("/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/Jiang/Seurat_object_IFNB_Perturb_seq.RNA.counts.aligned.h5ad")
    
    if not test_file.exists():
        logger.error(f"测试文件不存在: {test_file}")
        return False
    
    # 输出目录
    output_dir = test_file.parent / "test_skip_norm_output"
    output_dir.mkdir(exist_ok=True)
    
    logger.info("=" * 60)
    logger.info("测试跳过归一化功能")
    logger.info("=" * 60)
    logger.info(f"输入文件: {test_file}")
    logger.info(f"输出目录: {output_dir}")
    
    try:
        result = process_one_file_optimized(
            infile=test_file,
            outdir=output_dir,
            perturbation_column="gene",
            control_label="non-targeting",
            residual_expression=0.30,
            cell_residual_expression=0.50,
            min_cells=30,
            layer=None,
            aggregate_transcripts=False,
            gene_name_col="gene_name",
            chr_col="chr",
            start_col="start",
            end_col="end",
            min_overlap_frac=0.5,
            dataset_name="JIANG_IFNB_test",
            norm_tol_abs=1.0,
            max_memory_gb=50.0,
            skip_normalization=True,  # 关键参数
        )
        
        if result == 0:
            logger.info("✓ 测试成功完成")
            
            # 检查输出文件
            expected_output = output_dir / f"{test_file.stem}.cellload.filtered.h5ad"
            if expected_output.exists():
                logger.info(f"✓ 输出文件已创建: {expected_output}")
                file_size = expected_output.stat().st_size / (1024*1024)
                logger.info(f"✓ 文件大小: {file_size:.1f} MB")
                return True
            else:
                logger.error("✗ 输出文件未创建")
                return False
        else:
            logger.error(f"✗ 处理失败，返回码: {result}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_skip_normalization()
    sys.exit(0 if success else 1)
