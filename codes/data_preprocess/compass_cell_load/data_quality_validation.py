#!/usr/bin/env python3
"""
单细胞数据质量校验脚本
比较不同处理方式（原始数据 vs KD处理数据）对扰动效果的影响
"""

import numpy as np
import pandas as pd
import scanpy as sc
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib_venn import venn3
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置scanpy
sc.settings.verbosity = 1
sc.settings.set_figure_params(dpi=300, facecolor='white')

class DataQualityValidator:
    def __init__(self, file_paths, labels, output_dir):
        """
        初始化数据质量校验器
        
        Parameters:
        -----------
        file_paths : list of str
            3个.h5ad文件的路径
        labels : list of str
            对应的数据集标签
        output_dir : str
            输出目录路径
        """
        self.file_paths = file_paths
        self.labels = labels
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.adatas = {}
        self.control_label = "non-targeting"
        
    def load_data(self):
        """加载所有数据集"""
        print("正在加载数据集...")
        
        for i, (path, label) in enumerate(zip(self.file_paths, self.labels)):
            print(f"  加载 {label}: {path}")
            adata = sc.read_h5ad(path)
            
            # 确保基因名在var.index中
            if 'gene_name' in adata.var.columns:
                adata.var_names = adata.var['gene_name'].astype(str)
            
            # 确保扰动信息在obs中
            if 'gene' not in adata.obs.columns:
                raise ValueError(f"数据集 {label} 缺少扰动信息列 'gene'")
            
            self.adatas[label] = adata
            print(f"    维度: {adata.n_obs} 细胞 × {adata.n_vars} 基因")
            print(f"    扰动类型: {adata.obs['gene'].nunique()} 种")
        
        print("数据加载完成\n")
    
    def find_common_perturbations(self, top_n=3):
        """
        找到所有数据集共有的扰动类型，并选择细胞数最多的前N个
        
        Parameters:
        -----------
        top_n : int
            选择的扰动数量
            
        Returns:
        --------
        list : 选定的扰动类型
        """
        print("分析共有扰动类型...")
        
        # 获取每个数据集的扰动类型
        all_perturbations = {}
        for label, adata in self.adatas.items():
            perturbations = adata.obs['gene'].value_counts()
            # 排除control组
            perturbations = perturbations[perturbations.index != self.control_label]
            all_perturbations[label] = perturbations
            print(f"  {label}: {len(perturbations)} 种扰动")
        
        # 找到共有的扰动类型
        common_perturbations = set(all_perturbations[self.labels[0]].index)
        for label in self.labels[1:]:
            common_perturbations &= set(all_perturbations[label].index)
        
        print(f"  共有扰动类型: {len(common_perturbations)} 种")
        
        # 按细胞数排序，选择前top_n个
        perturbation_counts = {}
        for pert in common_perturbations:
            total_cells = sum(all_perturbations[label][pert] for label in self.labels)
            perturbation_counts[pert] = total_cells
        
        selected_perturbations = sorted(perturbation_counts.items(), 
                                      key=lambda x: x[1], reverse=True)[:top_n]
        
        selected_names = [pert for pert, count in selected_perturbations]
        
        print("  选定的扰动类型:")
        for pert, count in selected_perturbations:
            print(f"    {pert}: {count} 个细胞")
        
        print()
        return selected_names
    
    def calculate_perturbation_effects(self, perturbations):
        """
        计算扰动效果
        
        Parameters:
        -----------
        perturbations : list
            要分析的扰动类型
            
        Returns:
        --------
        dict : 扰动效果数据
        """
        print("计算扰动效果...")
        
        effects_data = {}
        
        for pert in perturbations:
            print(f"  分析扰动: {pert}")
            effects_data[pert] = {}
            
            for label, adata in self.adatas.items():
                # 检查目标基因是否存在
                if pert not in adata.var_names:
                    print(f"    警告: {label} 中未找到基因 {pert}")
                    continue
                
                # 获取目标基因的表达数据
                gene_idx = adata.var_names.get_loc(pert)
                
                # 获取control组和扰动组的细胞
                control_mask = adata.obs['gene'] == self.control_label
                pert_mask = adata.obs['gene'] == pert
                
                if not control_mask.any() or not pert_mask.any():
                    print(f"    警告: {label} 中缺少control组或{pert}扰动组")
                    continue
                
                # 提取表达值
                if hasattr(adata.X, 'toarray'):
                    control_expr = adata.X[control_mask, gene_idx].toarray().flatten()
                    pert_expr = adata.X[pert_mask, gene_idx].toarray().flatten()
                else:
                    control_expr = adata.X[control_mask, gene_idx].flatten()
                    pert_expr = adata.X[pert_mask, gene_idx].flatten()
                
                # 计算control组平均值
                control_mean = np.mean(control_expr)
                
                # 根据数据类型计算扰动效果
                if label == self.labels[0]:  # 假设第一个是原始数据
                    # 原始数据使用比值
                    if control_mean > 0:
                        effects = pert_expr / control_mean
                    else:
                        effects = pert_expr  # 如果control_mean为0，直接使用原值
                else:
                    # KD处理数据使用差值（已经log1p转换）
                    effects = pert_expr - control_mean
                
                effects_data[pert][label] = effects
                
                print(f"    {label}: control_mean={control_mean:.3f}, "
                      f"pert_cells={len(pert_expr)}, effect_range=[{effects.min():.3f}, {effects.max():.3f}]")
        
        print()
        return effects_data
    
    def plot_perturbation_effects(self, effects_data):
        """绘制扰动效果分布图"""
        print("绘制扰动效果分布图...")
        
        fig, axes = plt.subplots(1, len(effects_data), figsize=(5*len(effects_data), 4))
        if len(effects_data) == 1:
            axes = [axes]
        
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
        
        for i, (pert, data) in enumerate(effects_data.items()):
            ax = axes[i]
            
            for j, (label, effects) in enumerate(data.items()):
                # 绘制密度分布
                ax.hist(effects, bins=50, alpha=0.6, density=True, 
                       color=colors[j], label=label)
                
                # 添加均值线
                mean_val = np.mean(effects)
                ax.axvline(mean_val, color=colors[j], linestyle='--', alpha=0.8)
            
            ax.set_title(f'{pert} Perturbation Effects', fontsize=12, fontweight='bold')
            ax.set_xlabel('Effect Size')
            ax.set_ylabel('Density')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self.output_dir / 'perturbation_effects_distribution.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  扰动效果分布图已保存: {output_file}")
        print()
    
    def calculate_highly_variable_genes(self, perturbations, top_n=1024):
        """
        计算高可变基因
        
        Parameters:
        -----------
        perturbations : list
            要分析的扰动类型
        top_n : int
            选择的高可变基因数量
            
        Returns:
        --------
        dict : 高可变基因数据
        """
        print(f"计算高可变基因 (top {top_n})...")
        
        hvg_data = {}
        
        for pert in perturbations:
            print(f"  分析扰动: {pert}")
            hvg_data[pert] = {}
            
            for label, adata in self.adatas.items():
                # 获取control组和扰动组的细胞
                control_mask = adata.obs['gene'] == self.control_label
                pert_mask = adata.obs['gene'] == pert
                
                if not control_mask.any() or not pert_mask.any():
                    continue
                
                # 计算每个基因的变异系数或其他可变性指标
                if hasattr(adata.X, 'toarray'):
                    control_expr = adata.X[control_mask, :].toarray()
                    pert_expr = adata.X[pert_mask, :].toarray()
                else:
                    control_expr = adata.X[control_mask, :]
                    pert_expr = adata.X[pert_mask, :]
                
                # 计算每个基因的平均表达差异
                control_mean = np.mean(control_expr, axis=0)
                pert_mean = np.mean(pert_expr, axis=0)
                
                # 计算变化幅度（绝对值）
                if label == self.labels[0]:  # 原始数据
                    # 使用fold change
                    fold_change = np.abs(np.log2((pert_mean + 1) / (control_mean + 1)))
                else:
                    # KD处理数据使用差值的绝对值
                    fold_change = np.abs(pert_mean - control_mean)
                
                # 选择top_n个高可变基因
                top_indices = np.argsort(fold_change)[-top_n:]
                hvg_genes = set(adata.var_names[top_indices])
                
                hvg_data[pert][label] = hvg_genes
                
                print(f"    {label}: 选择了 {len(hvg_genes)} 个高可变基因")
        
        print()
        return hvg_data

    def plot_hvg_venn_diagrams(self, hvg_data):
        """绘制高可变基因的Venn图"""
        print("绘制高可变基因Venn图...")

        fig, axes = plt.subplots(1, len(hvg_data), figsize=(6*len(hvg_data), 5))
        if len(hvg_data) == 1:
            axes = [axes]

        for i, (pert, data) in enumerate(hvg_data.items()):
            ax = axes[i]

            if len(data) == 3:
                # 三个数据集的Venn图
                sets = [data[label] for label in self.labels]
                venn = venn3(sets, set_labels=self.labels, ax=ax)

                # 设置颜色
                if venn.get_patch_by_id('100'):
                    venn.get_patch_by_id('100').set_color('#1f77b4')
                if venn.get_patch_by_id('010'):
                    venn.get_patch_by_id('010').set_color('#ff7f0e')
                if venn.get_patch_by_id('001'):
                    venn.get_patch_by_id('001').set_color('#2ca02c')

            ax.set_title(f'{pert}\nHighly Variable Genes Overlap',
                        fontsize=12, fontweight='bold')

        plt.tight_layout()

        # 保存图片
        output_file = self.output_dir / 'hvg_venn_diagrams.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"  高可变基因Venn图已保存: {output_file}")
        print()

    def generate_summary_report(self, perturbations, effects_data, hvg_data):
        """生成总结报告"""
        print("生成总结报告...")

        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("数据质量校验报告")
        report_lines.append("=" * 60)
        report_lines.append("")

        # 数据集信息
        report_lines.append("数据集信息:")
        for label, adata in self.adatas.items():
            report_lines.append(f"  {label}: {adata.n_obs} 细胞 × {adata.n_vars} 基因")
        report_lines.append("")

        # 扰动分析结果
        report_lines.append("扰动效果分析:")
        for pert in perturbations:
            report_lines.append(f"  {pert}:")
            if pert in effects_data:
                for label, effects in effects_data[pert].items():
                    mean_effect = np.mean(effects)
                    std_effect = np.std(effects)
                    report_lines.append(f"    {label}: 平均效果={mean_effect:.3f}, 标准差={std_effect:.3f}")
            report_lines.append("")

        # 高可变基因分析结果
        report_lines.append("高可变基因分析:")
        for pert in perturbations:
            report_lines.append(f"  {pert}:")
            if pert in hvg_data:
                # 计算重叠统计
                sets = [hvg_data[pert][label] for label in self.labels if label in hvg_data[pert]]
                if len(sets) >= 2:
                    intersection_all = set.intersection(*sets)
                    union_all = set.union(*sets)

                    report_lines.append(f"    总基因数: {len(union_all)}")
                    report_lines.append(f"    共同基因数: {len(intersection_all)}")
                    report_lines.append(f"    重叠比例: {len(intersection_all)/len(union_all)*100:.1f}%")

                    # 两两重叠
                    for i in range(len(self.labels)):
                        for j in range(i+1, len(self.labels)):
                            if self.labels[i] in hvg_data[pert] and self.labels[j] in hvg_data[pert]:
                                set1 = hvg_data[pert][self.labels[i]]
                                set2 = hvg_data[pert][self.labels[j]]
                                overlap = len(set1 & set2)
                                union = len(set1 | set2)
                                report_lines.append(f"    {self.labels[i]} vs {self.labels[j]}: {overlap}/{union} ({overlap/union*100:.1f}%)")
            report_lines.append("")

        # 保存报告
        report_file = self.output_dir / 'quality_validation_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"  总结报告已保存: {report_file}")
        print()

        # 同时打印到控制台
        for line in report_lines:
            print(line)

    def run_validation(self, top_perturbations=3, top_hvg=1024):
        """运行完整的数据质量校验流程"""
        print("开始数据质量校验流程...\n")

        # 1. 加载数据
        self.load_data()

        # 2. 找到共有扰动
        perturbations = self.find_common_perturbations(top_perturbations)

        # 3. 计算扰动效果
        effects_data = self.calculate_perturbation_effects(perturbations)

        # 4. 绘制扰动效果分布图
        self.plot_perturbation_effects(effects_data)

        # 5. 计算高可变基因
        hvg_data = self.calculate_highly_variable_genes(perturbations, top_hvg)

        # 6. 绘制Venn图
        self.plot_hvg_venn_diagrams(hvg_data)

        # 7. 生成总结报告
        self.generate_summary_report(perturbations, effects_data, hvg_data)

        print("数据质量校验完成！")
        print(f"所有结果已保存到: {self.output_dir}")


def main():
    """主函数 - 示例用法"""

    # 示例文件路径（请根据实际情况修改）
    file_paths = [
        "/path/to/original_data.h5ad",           # 原始数据
        "/path/to/kd_processed_data1.h5ad",     # KD处理数据1
        "/path/to/kd_processed_data2.h5ad"      # KD处理数据2
    ]

    # 数据集标签
    labels = [
        "Original",
        "KD_Processed_1",
        "KD_Processed_2"
    ]

    # 输出目录
    output_dir = "/path/to/output"

    # 创建校验器并运行
    validator = DataQualityValidator(file_paths, labels, output_dir)
    validator.run_validation(top_perturbations=3, top_hvg=1024)


if __name__ == "__main__":
    main()
