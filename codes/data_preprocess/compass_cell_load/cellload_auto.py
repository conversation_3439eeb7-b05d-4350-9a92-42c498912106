#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import h5py
import os
# 限制数值库线程
os.environ.setdefault("OMP_NUM_THREADS", "1")
os.environ.setdefault("MKL_NUM_THREADS", "1")
os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")
os.environ.setdefault("NUMEXPR_NUM_THREADS", "1")

import argparse
import gc
import psutil
from pathlib import Path
from contextlib import contextmanager
import sys
import numpy as np
import pandas as pd
import anndata as ad
import scipy.sparse as sp
import inspect
from pandas.api.types import is_categorical_dtype
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from functools import partial
import logging
import warnings
import traceback
from data_filters import filter_on_target_knockdown

# 忽略一些常见警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# ---- 轻量进度条工具：tqdm 可选，不在就静默 ----
try:
    from tqdm import tqdm
except Exception:
    tqdm = None

class _NoTqdm:
    def __init__(self, *args, **kwargs): self.n = 0
    def update(self, n=1): self.n += n
    def close(self): pass
    def __enter__(self): return self
    def __exit__(self, exc_type, exc, tb): pass

def make_pbar(total: int, desc: str, unit: str = ""):
    use = os.environ.get("PROGRESS", "1").lower() in ("1","true","yes","y")
    if use and tqdm is not None and sys.stderr.isatty():
        # mininterval 限制刷新频率，避免刷新过快导致性能下降
        return tqdm(total=total, desc=desc, unit=unit, leave=False, mininterval=0.5)
    return _NoTqdm()


# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
def _scrub_obs_var_dtypes_inplace(adata):
    """
    将 obs/var 中容易在切片/拼接时报错的扩展 dtype 统一成稳定类型：
    - pandas 扩展整数/浮点/布尔/string/Arrow -> object 或 numpy 基础类型
    - 混合型/可疑的 Categorical -> 转为字符串 object
    """
    import pandas as pd
    from pandas.api.types import (
        is_categorical_dtype, is_bool_dtype, is_integer_dtype,
        is_float_dtype, is_string_dtype
    )

    def _fix_df(df):
        for c in list(df.columns):
            col = df[c]
            dt = getattr(col, "dtype", None)

            # 1) 先处理 Categorical（最容易炸）
            if is_categorical_dtype(dt):
                # 若类别里有混合类型或出现无法安全下发的类型，直接转字符串
                try:
                    cats = col.cat.categories
                    # 判断类别是否“统一为字符串”
                    if not all(isinstance(x, str) for x in cats):
                        df[c] = col.astype(str)
                    else:
                        # 类别是字符串也可能和 NA 混；保险起见转 object
                        df[c] = col.astype(object)
                except Exception:
                    df[c] = col.astype(str)
                continue

            # 2) pandas 扩展 string/boolean -> object（最稳）
            if is_string_dtype(dt) or str(dt).startswith("string["):
                df[c] = col.astype(object)
                continue
            if str(dt) == "boolean":
                # 保留缺失信息 -> object；若你偏好 numpy bool_ 可 col.fillna(False).astype(bool)
                df[c] = col.astype(object)
                continue

            # 3) 扩展整数/浮点（带 NA 的 Int64/Float64） -> object 或 numpy 基础型
            if str(dt).startswith("Int") or str(dt).startswith("Float"):
                # 若无缺失，可下压到 numpy；否则用 object 更稳
                try:
                    if not col.isna().any():
                        if is_integer_dtype(dt):
                            df[c] = col.astype(np.int64, copy=False)
                        elif is_float_dtype(dt):
                            df[c] = col.astype(np.float32, copy=False)
                        else:
                            df[c] = col.astype(object)
                    else:
                        df[c] = col.astype(object)
                except Exception:
                    df[c] = col.astype(object)
                continue

            # 4) 其他奇怪的 dtype（ArrowDtype 等）
            if "Arrow" in str(dt) or "pyarrow" in str(dt):
                df[c] = col.astype(object)
                continue

    _fix_df(adata.obs)
    _fix_df(adata.var)

def monitor_memory():
    """监控内存使用"""
    process = psutil.Process(os.getpid())
    mem_info = process.memory_info()
    return mem_info.rss / (1024**3)  # GB
def estimate_sparse_memory_usage(shape, nnz):
    """估算稀疏矩阵内存使用"""
    n_rows, n_cols = shape
    # CSR格式：data(float32) + indices(int32) + indptr(int32)
    data_bytes = nnz * 4  # float32
    indices_bytes = nnz * 4  # int32 instead of int64
    indptr_bytes = (n_rows + 1) * 4  # int32
    total_bytes = data_bytes + indices_bytes + indptr_bytes
    return total_bytes / (1024**3)  # GB
def safe_sparse_conversion(X, max_memory_gb=50):
    """安全的稀疏矩阵转换，避免内存爆炸"""
    if not sp.issparse(X):
        return X
    
    # 检查当前矩阵大小
    if hasattr(X, 'nnz'):
        estimated_gb = estimate_sparse_memory_usage(X.shape, X.nnz)
        if estimated_gb > max_memory_gb:
            logger.warning(f"Sparse matrix too large ({estimated_gb:.1f}GB), using streaming approach")
            return convert_sparse_streaming(X, max_memory_gb)
    
    try:
        X = X.tocsr()
        # 使用int32而不是int64来节省内存
        if X.indices.dtype == np.int64:
            max_index = X.indices.max() if X.indices.size > 0 else 0
            if max_index < np.iinfo(np.int32).max:
                X.indices = X.indices.astype(np.int32)
        
        if X.indptr.dtype == np.int64:
            max_ptr = X.indptr.max() if X.indptr.size > 0 else 0
            if max_ptr < np.iinfo(np.int32).max:
                X.indptr = X.indptr.astype(np.int32)
                
        # 确保数据类型为float32
        if X.data.dtype != np.float32:
            X.data = X.data.astype(np.float32)
            
        return X
    except MemoryError as e:
        logger.error(f"Standard conversion failed: {e}")
        return convert_sparse_streaming(X, max_memory_gb)
def convert_sparse_streaming(X, max_memory_gb=50):
    """流式转换大型稀疏矩阵"""
    logger.info("Using streaming conversion for large sparse matrix")
    
    if not sp.issparse(X):
        return X
    
    X = X.tocsr()
    n_rows, n_cols = X.shape
    
    # 计算安全的batch大小
    bytes_per_row = X.nnz * 8 / n_rows  # 估算每行字节数
    max_rows_per_batch = int(max_memory_gb * 1024**3 / bytes_per_row / 4)  # 安全系数
    max_rows_per_batch = max(1000, min(max_rows_per_batch, 100000))
    
    logger.info(f"Processing {n_rows} rows in batches of {max_rows_per_batch}")
    
    # 流式处理
    row_batches = []
    for start_row in range(0, n_rows, max_rows_per_batch):
        end_row = min(start_row + max_rows_per_batch, n_rows)
        
        try:
            batch = X[start_row:end_row].copy()
            batch = safe_sparse_conversion(batch, max_memory_gb/4)
            row_batches.append(batch)
            
            if len(row_batches) % 10 == 0:
                logger.info(f"Processed {len(row_batches)} batches")
                gc.collect()
                
        except MemoryError:
            logger.error(f"Failed to process batch {start_row}:{end_row}")
            continue
    
    if not row_batches:
        raise MemoryError("No batches could be processed")
    
    # 合并batches
    logger.info(f"Combining {len(row_batches)} batches")
    try:
        result = sp.vstack(row_batches, format='csr')
        return safe_sparse_conversion(result, max_memory_gb)
    except MemoryError:
        # 如果合并失败，返回第一个batch作为示例
        logger.error("Failed to combine batches, returning first batch only")
        return row_batches[0]

def load_subset_memory_efficient(infile, perturbation_column, control_label, min_cells):
    """内存高效的子集加载函数"""
    
    file_size_gb = os.path.getsize(infile) / (1024**3)
    available_memory_gb = psutil.virtual_memory().available / (1024**3)
    
    # 动态调整chunk大小
    if file_size_gb > 50:
        chunk_size = 50000   # 超大文件用小chunk
    elif file_size_gb > 20:
        chunk_size = 100000  # 大文件用中等chunk
    else:
        chunk_size = 200000  # 小文件用大chunk
    
    logger.info(f"Loading {file_size_gb:.1f}GB file with chunk_size={chunk_size}")
    logger.info(f"Available memory: {available_memory_gb:.1f}GB")
    
    # 设置HDF5缓存 - 更保守
    rdcc_bytes = min(4*1024*1024*1024, int(available_memory_gb * 0.1 * 1024**3))
    
    try:
        b = ad.read_h5ad(infile, backed="r", 
                       cache={'rdcc_nbytes': rdcc_bytes})
    except TypeError:
        b = ad.read_h5ad(infile, backed="r")
    
    if perturbation_column not in b.obs.columns:
        raise KeyError(f"Column {perturbation_column} not found")

    n_obs = b.n_obs
    logger.info(f"Total observations: {n_obs:,}")
    
    # 第一遍：统计扰动
    pert_counts = {}
    logger.info("Counting perturbations...")
    
    for start_idx in range(0, n_obs, chunk_size):
        end_idx = min(start_idx + chunk_size, n_obs)
        chunk_obs = b.obs.iloc[start_idx:end_idx][perturbation_column]
        
        for pert, count in chunk_obs.value_counts().items():
            pert_counts[str(pert)] = pert_counts.get(str(pert), 0) + count
        
        if start_idx % 1000000 == 0 and start_idx > 0:
            logger.info(f"Processed {start_idx:,} cells for counting")
        
        # 定期清理
        if start_idx % (chunk_size * 10) == 0:
            gc.collect()
    
    # 确定保留的扰动
    keep_perts = {str(control_label)}
    for pert, count in pert_counts.items():
        if count >= min_cells:
            keep_perts.add(pert)
    
    logger.info(f"Keeping {len(keep_perts)} perturbations out of {len(pert_counts)}")
    
    # 第二遍：收集索引 - 使用更小的批次
    keep_indices = []
    logger.info("Collecting cell indices...")
    
    for start_idx in range(0, n_obs, chunk_size):
        end_idx = min(start_idx + chunk_size, n_obs)
        chunk_obs = b.obs.iloc[start_idx:end_idx][perturbation_column]
        chunk_obs = chunk_obs.astype(str)
        
        chunk_keep_mask = chunk_obs.isin(keep_perts)
        local_keep_indices = np.where(chunk_keep_mask)[0] + start_idx
        keep_indices.extend(local_keep_indices.tolist())
        
        if start_idx % 1000000 == 0 and start_idx > 0:
            logger.info(f"Processed {start_idx:,} cells, found {len(keep_indices):,} to keep")
        
        # 更频繁的内存清理
        if len(keep_indices) > 1000000 and start_idx % (chunk_size * 5) == 0:
            gc.collect()
    
    # 使用平台整数（np.intp），避免底层索引路径的 dtype 约束问题
    keep_indices = np.array(keep_indices, dtype=np.intp)

    keep_indices.sort()
    
    n_keep = len(keep_indices)
    keep_ratio = n_keep / n_obs if n_obs > 0 else 0.0
    
    logger.info(f"Keep statistics: {n_keep:,}/{n_obs:,} cells ({keep_ratio:.1%})")
    
    # 内存高效的子集提取
    if keep_ratio > 0.8 and available_memory_gb > file_size_gb * 4:
        logger.info("High keep ratio and sufficient memory, attempting full load")
        try:
            return load_full_and_subset_careful(b, keep_indices, available_memory_gb * 0.6)
        except MemoryError:
            logger.warning("Full load failed, falling back to streaming")
    
    # 流式加载
    return load_streaming_batched(b, keep_indices, chunk_size)

def load_full_and_subset_careful(b, keep_indices, max_memory_gb):
    """小心的全量加载和子集化"""
    logger.info("Attempting careful full load...")
    
    try:
        # 分步骤加载
        logger.info("Step 1: Loading to memory...")
        full = b.to_memory()
        
        logger.info("Step 2: Converting sparse matrix...")
        if sp.issparse(full.X):
            full.X = safe_sparse_conversion(full.X, max_memory_gb/2)
        
        logger.info("Step 3: Creating subset...")
        # 使用更安全的子集化方法
        sub = create_subset_safe(full, keep_indices, max_memory_gb/3)
        
        del full
        gc.collect()
        return sub
        
    except MemoryError as e:
        logger.error(f"Full load failed: {e}")
        raise

def create_subset_safe(adata, keep_indices, max_memory_gb):
    """安全的子集创建"""
    n_keep = len(keep_indices)
    _scrub_obs_var_dtypes_inplace(adata)
    if n_keep < 100000:
        # 小子集，直接处理
        return adata[keep_indices].copy()
    
    # 大子集，分批处理
    logger.info(f"Creating subset with {n_keep:,} cells using batched approach")
    
    batch_size = min(50000, int(max_memory_gb * 1024**3 / (adata.n_vars * 4 * 8)))
    batch_size = max(1000, batch_size)
    
    sub_chunks = []
    for i in range(0, n_keep, batch_size):
        batch_end = min(i + batch_size, n_keep)
        batch_indices = keep_indices[i:batch_end]
        
        try:
            chunk = adata[batch_indices].copy()
            if sp.issparse(chunk.X):
                chunk.X = safe_sparse_conversion(chunk.X, max_memory_gb/10)
            sub_chunks.append(chunk)
            
            if len(sub_chunks) % 5 == 0:
                logger.info(f"Created {len(sub_chunks)} subset chunks")
                gc.collect()
                
        except Exception as e:
            logger.error(f"Failed to create chunk {i}:{batch_end}: {e}")
            continue
    
    if not sub_chunks:
        raise ValueError("No valid subset chunks created")
    
    # 合并chunks
    logger.info(f"Combining {len(sub_chunks)} chunks...")
    if len(sub_chunks) == 1:
        return sub_chunks[0]
    
    try:
        result = ad.concat(sub_chunks, axis=0, join='outer')
        # 清理临时chunks
        for chunk in sub_chunks:
            del chunk
        gc.collect()
        return result
    except MemoryError:
        logger.error("Failed to combine chunks")
        return sub_chunks[0]  # 返回第一个chunk

def load_streaming_batched(b, keep_indices, chunk_size):
    """流式批量加载"""
    logger.info("Using streaming batched loading")
    
    # 将keep_indices转换为区间
    def indices_to_runs(indices):
        if len(indices) == 0:
            return []
        
        runs = []
        start = indices[0]
        end = start
        
        for i in range(1, len(indices)):
            if indices[i] == end + 1:
                end = indices[i]
            else:
                runs.append((start, end + 1))
                start = indices[i]
                end = start
        runs.append((start, end + 1))
        return runs
    
    runs = indices_to_runs(keep_indices)
    logger.info(f"Created {len(runs)} contiguous runs")
    
    # 合并小的相近runs
    merged_runs = []
    for start, end in runs:
        if merged_runs and start - merged_runs[-1][1] < 1000:
            merged_runs[-1] = (merged_runs[-1][0], end)
        else:
            merged_runs.append((start, end))
    
    logger.info(f"Merged to {len(merged_runs)} runs")
    
    chunks = []
    for i, (start, end) in enumerate(merged_runs):
        try:
            logger.info(f"Loading run {i+1}/{len(merged_runs)}: {start:,}-{end:,} ({end-start:,} cells)")
            
            # 分批加载大的runs
            for batch_start in range(start, end, chunk_size):
                batch_end = min(batch_start + chunk_size, end)
                
                try:
                    whole = b[batch_start:batch_end].to_memory()
                    if whole.n_obs == 0:
                        continue
                    
                    # 计算本地索引
                    global_indices = np.arange(batch_start, batch_end)
                    local_keep_mask = np.isin(global_indices, keep_indices)
                    
                    if not local_keep_mask.any():
                        del whole
                        continue
                    
                    # 创建子集
                    if local_keep_mask.all():
                        chunk = whole
                    else:
                        local_indices = np.where(local_keep_mask)[0]
                        chunk = whole[local_indices].copy()
                        del whole
                    
                    # 优化矩阵
                    if sp.issparse(chunk.X):
                        chunk.X = safe_sparse_conversion(chunk.X, 10)  # 10GB限制
                    
                    chunks.append(chunk)
                    
                    if len(chunks) % 10 == 0:
                        logger.info(f"Loaded {len(chunks)} chunks")
                        gc.collect()
                        
                except Exception as e:
                    logger.error(f"Failed to load batch {batch_start}:{batch_end}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Failed to process run {start}:{end}: {e}")
            continue
    
    if not chunks:
        raise ValueError("No chunks could be loaded")
    
    # 合并所有chunks
    logger.info(f"Combining {len(chunks)} final chunks...")
    if len(chunks) == 1:
        result = chunks[0]
    else:
        try:
            result = ad.concat(chunks, axis=0, join='outer')
        except MemoryError:
            logger.error("Final concatenation failed, returning first chunk")
            result = chunks[0]
    
    # 清理
    for chunk in chunks:
        del chunk
    gc.collect()
    
    try:
        b.file.close()
    except:
        pass
    
    return result


def force_gc():
    """强制垃圾回收"""
    import gc
    for _ in range(3):
        gc.collect()
    # 添加系统级内存清理提示
    try:
        import ctypes
        libc = ctypes.CDLL("libc.so.6")
        libc.malloc_trim(0)
    except:
        pass
# 修改环境变量设置，利用大内存优势
def setup_memory_efficient_environment():
    """设置内存高效环境"""
    # 更保守的内存设置
    os.environ["OMP_NUM_THREADS"] = "2"
    os.environ["MKL_NUM_THREADS"] = "2"
    os.environ["LOAD_CHUNK_ROWS"] = "100000"    # 减小chunk size
    os.environ["MAX_SPARSE_INDEX_SIZE"] = "50000000000"  # 50GB限制
    
    # Python内存优化 - 更严格
    os.environ["MALLOC_ARENA_MAX"] = "4"
    os.environ["MALLOC_MMAP_THRESHOLD_"] = "32768"
    
    logger.info("Memory-efficient environment configured")
@contextmanager
def memory_monitor(operation_name):
    """内存监控上下文管理器"""
    start_mem = monitor_memory()
    logger.info(f"[{operation_name}] Start - Memory: {start_mem:.2f} GB")
    try:
        yield
    finally:
        force_gc()
        end_mem = monitor_memory()
        logger.info(f"[{operation_name}] End - Memory: {end_mem:.2f} GB (Δ: {end_mem-start_mem:+.2f} GB)")

def safe_astype(series, target_dtype, fallback_dtype=str):
    """修复的安全类型转换函数"""
    try:
        # 处理特殊情况：已经是目标类型
        if hasattr(series, 'dtype') and series.dtype == target_dtype:
            return series
            
        # 如果是pandas Series或DataFrame列
        if hasattr(series, 'astype'):
            # 处理分类数据
            if target_dtype == 'category':
                # 先转换为字符串，再转为category，避免scalar问题
                str_series = series.astype(str)
                return pd.Categorical(str_series)
            else:
                return series.astype(target_dtype)
        else:
            # 如果是numpy数组或其他类型
            if target_dtype == 'category':
                # 转为字符串列表再创建category
                str_list = [str(x) for x in series]
                return pd.Categorical(str_list)
            else:
                # 使用numpy处理
                return np.asarray(series, dtype=target_dtype)
                
    except (ValueError, TypeError, OverflowError) as e:
        logger.warning(f"Type conversion to {target_dtype} failed: {e}, using fallback {fallback_dtype}")
        try:
            if fallback_dtype == 'category':
                if hasattr(series, 'astype'):
                    str_series = series.astype(str)
                    return pd.Categorical(str_series)
                else:
                    str_list = [str(x) for x in series]
                    return pd.Categorical(str_list)
            else:
                if hasattr(series, 'astype'):
                    return series.astype(fallback_dtype)
                else:
                    return np.asarray(series, dtype=fallback_dtype)
        except Exception as e2:
            logger.error(f"Fallback conversion also failed: {e2}")
            # 最后的备用方案：强制转换为字符串
            if hasattr(series, 'astype'):
                return series.astype(str)
            else:
                return np.asarray([str(x) for x in series])

def safe_matrix_conversion(X, target_dtype=np.float32):
    """修复的安全矩阵类型转换"""
    try:
        if sp.issparse(X):
            X = X.tocsr()  # 确保是CSR格式
            # 对于稀疏矩阵，只转换data部分
            if hasattr(X, 'data') and len(X.data) > 0:
                # 检查数据类型，避免integer scalar问题
                if X.data.dtype == target_dtype:
                    return X
                # 创建新的数据数组而不是就地转换
                new_data = np.asarray(X.data, dtype=target_dtype)
                indices = X.indices.astype(np.int32, copy=False) if X.indices.dtype != np.int32 else X.indices
                indptr  = X.indptr.astype(np.int32,  copy=False) if X.indptr.dtype  != np.int32 else X.indptr
                X_new = sp.csr_matrix((new_data, indices, indptr), shape=X.shape)
                return X_new

            return X
        else:
            # 对于密集矩阵
            if hasattr(X, 'dtype') and X.dtype == target_dtype:
                return X
            return np.asarray(X, dtype=target_dtype)
    except Exception as e:
        logger.error(f"Matrix conversion failed: {e}")
        # 更保守的降级处理
        try:
            if sp.issparse(X):
                # 创建新的稀疏矩阵
                X_csr = X.tocsr()
                if X_csr.data.size > 0:
                    new_data = np.array(X_csr.data, dtype=target_dtype, copy=True)
                    return sp.csr_matrix((new_data, X_csr.indices, X_csr.indptr), shape=X.shape)
                else:
                    return sp.csr_matrix(X.shape, dtype=target_dtype)
            else:
                # 创建新的密集矩阵
                return np.array(X, dtype=target_dtype, copy=True)
        except Exception as e2:
            logger.error(f"Fallback matrix conversion also failed: {e2}")
            # 最后尝试：创建一个新的零矩阵
            if sp.issparse(X):
                return sp.csr_matrix(X.shape, dtype=target_dtype)
            else:
                return np.zeros(X.shape, dtype=target_dtype)

def _ensure_csr(X):
    """确保稀疏矩阵为CSR格式"""
    if sp.issparse(X) and not sp.isspmatrix_csr(X):
        return X.tocsr()
    return X

def _nan_to_num_inplace(X):
    """
    安全版：仅对浮点数组做就地 nan->数值 的处理。
    """
    arr = np.asarray(X)
    # 非浮点：不处理
    if not np.issubdtype(arr.dtype, np.floating):
        return X
    try:
        # 避免使用out=参数，直接赋值
        cleaned = np.nan_to_num(arr, copy=False)
        if cleaned is not arr:
            arr[:] = cleaned
    except Exception as e:
        logger.warning(f"NaN cleaning failed: {e}")
    return X

def _nan_to_zero_inplace(adata):
    """原地将NaN转换为0，优化内存使用"""
    try:
        X = adata.X
        if sp.issparse(X):
            X = _ensure_csr(X)
            if hasattr(X.data, '__len__') and len(X.data) > 0:
                nan_mask = np.isnan(X.data)
                if nan_mask.any():
                    # 创建新的数据数组而不是就地修改
                    new_data = X.data.copy()
                    new_data[nan_mask] = 0.0
                    adata.X = sp.csr_matrix((new_data, X.indices, X.indptr), shape=X.shape)
                    adata.X.eliminate_zeros()
        else:
            if X.size > 0:
                X = safe_matrix_conversion(X, np.float32)
                _nan_to_num_inplace(X)
                adata.X = X
    except Exception as e:
        logger.warning(f"NaN conversion failed: {e}")

def _standardize_names_and_control(
    adata,
    perturbation_column: str,
    gene_name_col: str,
    control_label: str,
    target_ctrl_label: str = "non-targeting",
):
    """修复的标准化函数"""
    with memory_monitor("standardize_names"):
        try:
            logger.info(f"Standardizing: pert_col={perturbation_column}, gene_col={gene_name_col}")
            logger.info(f"Control label: {control_label} -> {target_ctrl_label}")
            
            # 处理扰动列
            if perturbation_column not in adata.obs.columns:
                raise KeyError(f"Column '{perturbation_column}' not found in obs")
                
            s = adata.obs[perturbation_column]
            logger.info(f"Perturbation column type: {type(s)}, dtype: {s.dtype if hasattr(s, 'dtype') else 'N/A'}")
            
            # 安全地转换为字符串，避免integer scalar问题
            s_str = safe_astype(s, str)
            
            if control_label != target_ctrl_label:
                # 替换控制标签
                s_str = s_str.replace(control_label, target_ctrl_label)
            
                # s_str 先转成 pandas StringDtype，保证不是 numpy 标量混杂
                s_str = pd.Series(safe_astype(s, str), index=adata.obs.index, dtype="string")
                if control_label != target_ctrl_label:
                    s_str = s_str.replace({control_label: target_ctrl_label})

                # 用 Series + dtype='category'，而不是裸 Categorical
                adata.obs["gene"] = s_str.astype("category")


            # 处理基因名列
            if gene_name_col not in adata.var.columns:
                logger.warning(f"Gene name column '{gene_name_col}' not found, using index")
                adata.var["gene_name"] = adata.var.index.astype(str)
            else:
                if gene_name_col != "gene_name":
                    adata.var["gene_name"] = safe_astype(adata.var[gene_name_col], str)
                else:
                    adata.var["gene_name"] = safe_astype(adata.var["gene_name"], str)

            # 处理NaN值
            _nan_to_zero_inplace(adata)
            
        except Exception as e:
            logger.error(f"Error in standardize_names_and_control: {e}")
            # 尝试简单的回退方案
            try:
                adata.obs["gene"] = safe_astype(adata.obs[perturbation_column], str)
                if gene_name_col in adata.var.columns:
                    adata.var["gene_name"] = safe_astype(adata.var[gene_name_col], str)
                else:
                    adata.var["gene_name"] = adata.var.index.astype(str)
            except Exception as simple_e:
                logger.error(f"Simple fallback also failed: {simple_e}")
                raise


def _to_runs(sorted_idx: np.ndarray):
    """将排序的索引转换为连续区间"""
    if sorted_idx.size == 0:
        return []
    split = np.where(np.diff(sorted_idx) != 1)[0] + 1
    parts = np.split(sorted_idx, split)
    return [(int(p[0]), int(p[-1]) + 1) for p in parts]

def load_subset_min_cells_only_high_memory(infile, perturbation_column, control_label, min_cells):
    """替换原有函数的内存高效版本"""
    setup_memory_efficient_environment()
    return load_subset_memory_efficient(infile, perturbation_column, control_label, min_cells)
@contextmanager
def use_local_pkg(root: str):
    """使用本地包的上下文管理器"""
    sys.path.insert(0, root)
    try:
        yield
    finally:
        try:
            sys.path.remove(root)
        except ValueError:
            pass

def _get_X(adata, layer=None):
    return adata.layers[layer] if layer is not None else adata.X

def _row_sums(X):
    if sp.issparse(X):
        return np.asarray(X.sum(axis=1)).ravel()
    return X.sum(axis=1)

def _normalize_rows_to_target_inplace(X, target=1e4):
    if sp.issparse(X):
        # 一定要 copy；避免共享 indptr/indices 被意外复用
        X = X.tocsr(copy=True)

        # 确保 data 浮点
        if X.data.dtype.kind in "iu":
            X.data = X.data.astype(np.float32, copy=False)
        elif X.data.dtype != np.float32:
            X.data = X.data.astype(np.float32, copy=False)

        # 用 SciPy 的正规接口求行和，避免 reduceat 带来的空行歧义
        row_sums = np.asarray(X.sum(axis=1)).ravel().astype(np.float32, copy=False)

        nz = row_sums > 0
        if nz.any():
            scale = np.zeros_like(row_sums, dtype=np.float32)
            scale[nz] = target / row_sums[nz]
            # 按 CSR 行重复缩放因子
            counts = np.diff(X.indptr)
            X.data *= np.repeat(scale, counts)
        return X
    else:
        X = np.asarray(X, dtype=np.float32, order="C")
        row_sums = X.sum(axis=1, keepdims=True)
        nz = row_sums > 0
        if np.any(nz):
            X[nz] *= (target / row_sums[nz])
        return X


def _log1p_inplace_adatasX(adata):
    """修复的log1p变换"""
    try:
        X = adata.X
        if sp.issparse(X):
            X = _ensure_csr(X)
            if X.data is not None and X.data.size > 0:
                if X.data.dtype.kind in "iu":
                    # 创建新的数组而不是就地转换
                    new_data = np.array(X.data, dtype=np.float32)
                    X = sp.csr_matrix((new_data, X.indices, X.indptr), shape=X.shape)
                elif X.data.dtype != np.float32:
                    new_data = np.array(X.data, dtype=np.float32)
                    X = sp.csr_matrix((new_data, X.indices, X.indptr), shape=X.shape)
                
                # 安全的log1p计算
                np.clip(X.data, -0.999999, None, out=X.data)
                X.data[:] = np.log1p(X.data)
                
            adata.X = X
        else:
            X = safe_matrix_conversion(X, np.float32)
            np.clip(X, -0.999999, None, out=X)
            X[:] = np.log1p(X)
            adata.X = X
            
    except Exception as e:
        logger.error(f"Log1p transformation failed: {e}")
        raise ValueError(f"Log1p transformation failed: {e}")

def _expm1_inplace_adatasX(adata, layer=None):
    """修复的expm1变换"""
    X = _get_X(adata, layer)
    
    try:
        if sp.issparse(X):
            X = _ensure_csr(X.copy())
            if hasattr(X.data, '__len__') and len(X.data) > 0:
                if X.data.dtype.kind in "iu":
                    new_data = np.array(X.data, dtype=np.float32)
                    X = sp.csr_matrix((new_data, X.indices, X.indptr), shape=X.shape)
                elif X.data.dtype != np.float32:
                    new_data = np.array(X.data, dtype=np.float32)
                    X = sp.csr_matrix((new_data, X.indices, X.indptr), shape=X.shape)
                
                X.data[:] = np.expm1(X.data)
            return X
        else:
            X = safe_matrix_conversion(X, np.float32)
            X[:] = np.expm1(X)
            return X
            
    except Exception as e:
        logger.error(f"Expm1 transformation failed: {e}")
        raise ValueError(f"Expm1 transformation failed: {e}")

# 添加其他必要的辅助函数
def mark_log1p(adata, base=None):
    adata.uns["log1p"] = {"base": base}

def clear_log1p_flag(adata):
    adata.uns.pop("log1p", None)

def mark_norm_total(adata, target_sum=1e4, key="norm_per_cell"):
    pp = adata.uns.setdefault("pp", {})
    pp[key] = {"target_sum": float(target_sum), "method": "l1", "inplace": True}

def record_cellload_steps(adata, **kwargs):
    meta = adata.uns.setdefault("cellload_preproc", {})
    meta.update(kwargs)

def is_log1p_matrix(adata, layer=None, sample_cells=500, sample_genes=500, tol_1e4=0.05):
    """检测是否为log1p矩阵"""
    try:
        if isinstance(adata.uns.get("log1p", None), dict) or ("log1p" in adata.uns):
            return True, 0.0
    except Exception:
        pass

    try:
        pp = adata.uns.get("pp", {})
        if isinstance(pp, dict) and ("log1p" in pp or pp.get("recipe") == "log1p"):
            return True, 0.0
    except Exception:
        pass

    X = _get_X(adata, layer)
    n_cells, n_genes = adata.n_obs, adata.n_vars
    
    sample_cells = min(sample_cells, n_cells)
    sample_genes = min(sample_genes, n_genes)
    
    r_idx = np.arange(n_cells)
    c_idx = np.arange(n_genes)
    
    if n_cells > sample_cells:
        r_idx = np.random.default_rng(0).choice(n_cells, size=sample_cells, replace=False)
        r_idx.sort()
    if n_genes > sample_genes:
        c_idx = np.random.default_rng(1).choice(n_genes, size=sample_genes, replace=False)
        c_idx.sort()

    Xs = X[r_idx][:, c_idx]
    
    if sp.issparse(Xs):
        Xs = _ensure_csr(Xs)
        vals = Xs.data
    else:
        vals = np.asarray(Xs).ravel()

    if len(vals) == 0:
        return False, 0.0

    if np.min(vals) < 0:
        raise ValueError("参数错误：数据可能经过 Z-score 处理，不适用于此 pipeline")

    vmax = float(np.max(vals))
    if vmax > 20:
        return False, 0.0

    return True, 0.0

def sanitize_for_h5ad(adata):
    """在写出前清理obs/var"""
    try:
        adata.obs_names_make_unique()
    except Exception:
        pass
    try:
        adata.var_names_make_unique()
    except Exception:
        pass

    reserved = {"_index", "index", "obs_names", "var_names"}

    def _fix_df(df, axis_name: str):
        rename_map = {}
        drop_cols = []

        for col in list(df.columns):
            col_str = str(col)
            if col_str in reserved:
                try:
                    idx_vals = df.index.astype(str).values
                    same_as_index = False
                    try:
                        same_as_index = np.all(df[col].astype(str).values == idx_vals)
                    except Exception:
                        pass
                    if same_as_index:
                        drop_cols.append(col)
                    else:
                        base = f"{col_str}_col"
                        new = base
                        k = 1
                        while new in df.columns or new in reserved:
                            new = f"{base}{k}"
                            k += 1
                        rename_map[col] = new
                except Exception:
                    rename_map[col] = f"{col_str}_col"

        if rename_map:
            df.rename(columns=rename_map, inplace=True)
        if drop_cols:
            df.drop(columns=list(set(drop_cols)), inplace=True)

    _fix_df(adata.obs, "obs")
    _fix_df(adata.var, "var")

def ensure_h5sparse_metadata(adata):
    """确保稀疏矩阵有正确的h5sparse元数据"""
    if sp.issparse(adata.X):
        adata.uns.setdefault('__h5sparse_format__', 'csr')
        if hasattr(adata.X, 'format'):
            adata.uns['__h5sparse_format__'] = adata.X.format

def optimize_for_io(adata):
    """优化AnnData对象以提高IO性能"""
    try:
        for col in adata.obs.columns:
            if is_categorical_dtype(adata.obs[col]):
                cat_col = adata.obs[col]
                if len(cat_col.cat.categories) > len(cat_col.unique()) * 2:
                    adata.obs[col] = cat_col.cat.remove_unused_categories()
        
        for col in adata.var.columns:
            if is_categorical_dtype(adata.var[col]):
                cat_col = adata.var[col]
                if len(cat_col.cat.categories) > len(cat_col.unique()) * 2:
                    adata.var[col] = cat_col.cat.remove_unused_categories()
                    
        if sp.issparse(adata.X):
            adata.X = _ensure_csr(adata.X)
            
    except Exception as e:
        logger.warning(f"IO optimization failed: {e}")

def validate_data_integrity(adata, operation_name="unknown"):
    """验证数据完整性"""
    if adata.n_obs > 3000000:
        logger.info(f"Large dataset ({adata.n_obs} cells), skipping detailed validation")
        if adata.X is None:
            raise ValueError("X matrix is None")
        return
    try:
        logger.info(f"Validating data integrity for: {operation_name}")
        
        X = adata.X
        if X is None:
            raise ValueError("X matrix is None")
            
        if sp.issparse(X):
            if not sp.isspmatrix_csr(X):
                logger.warning("Converting sparse matrix to CSR format")
                adata.X = X.tocsr()
                X = adata.X
            
            if hasattr(X, 'data') and len(X.data) > 0:
                if not np.isfinite(X.data).all():
                    logger.warning("Found non-finite values in sparse matrix data")
                    X.data = np.nan_to_num(X.data, copy=False)
        else:
            if not np.isfinite(X).all():
                logger.warning("Found non-finite values in dense matrix")
                adata.X = np.nan_to_num(X, copy=False)
        
        if adata.n_obs == 0:
            raise ValueError("No observations in data")
        if adata.n_vars == 0:
            raise ValueError("No variables in data")
            
        logger.info(f"Data validation passed: {adata.n_obs} cells, {adata.n_vars} genes")
        
    except Exception as e:
        logger.error(f"Data validation failed for {operation_name}: {e}")
        raise

def clean_data_matrix(adata):
    """清理数据矩阵，确保数值有效性"""
    try:
        X = adata.X
        if sp.issparse(X):
            X = _ensure_csr(X)
            if hasattr(X.data, '__len__') and len(X.data) > 0:
                mask = ~np.isfinite(X.data.astype(np.float64))
                if mask.any():
                    logger.warning(f"Found {mask.sum()} non-finite values in sparse matrix, converting to 0")
                    new_data = X.data.astype(np.float64)
                    new_data[mask] = 0
                    adata.X = sp.csr_matrix((new_data.astype(np.float32), X.indices, X.indptr), shape=X.shape)
                    adata.X.eliminate_zeros()
        else:
            if X.size > 0:
                mask = ~np.isfinite(X.astype(np.float64))
                if mask.any():
                    logger.warning(f"Found {mask.sum()} non-finite values in dense matrix, converting to 0")
                    X = X.astype(np.float64)
                    X[mask] = 0
                    adata.X = X.astype(np.float32)
    except Exception as e:
        logger.warning(f"Data cleaning failed: {e}")

def process_one_file_optimized(
    infile: Path,
    outdir: Path = None,
    perturbation_column: str = "gene",
    control_label: str = "non-targeting",
    residual_expression: float = 0.30,
    cell_residual_expression: float = 0.50,
    min_cells: int = 30,
    layer: str | None = None,
    aggregate_transcripts: bool = False,
    gene_name_col: str = "gene_name",
    chr_col: str = "chr",
    start_col: str = "start",
    end_col: str = "end",
    min_overlap_frac: float = 0.5,
    dataset_name: str = "unknown",
    norm_tol_abs: float = 1.0,
    max_memory_gb: float = 600.0,
    skip_normalization: bool = False,
):
    """修复的单文件处理函数，增强错误处理和恢复能力"""
    
    file_size_gb = infile.stat().st_size / (1024**3)
    current_memory_gb = monitor_memory()
    available_memory = psutil.virtual_memory().available / (1024**3)
    
    estimated_memory_gb = max(file_size_gb * 3.5, 8)
    hard_cap = min(max_memory_gb, psutil.virtual_memory().available/1024**3 * 0.9)

    if estimated_memory_gb > hard_cap:
        os.environ["LOAD_CHUNK_ROWS"] = str(max(50000, int(int(os.environ.get("LOAD_CHUNK_ROWS","150000"))/2)))
        logger.warning(f"Estimated mem {estimated_memory_gb:.1f}GB > cap {hard_cap:.1f}GB; "
                    f"lowering LOAD_CHUNK_ROWS to {os.environ['LOAD_CHUNK_ROWS']} and continuing.")
    
    logger.info(f"Processing {infile.name}: {file_size_gb:.1f}GB file, "
                f"estimated memory: {estimated_memory_gb:.1f}GB, "
                f"available: {available_memory:.1f}GB")

    if outdir is None:
        outdir = infile.parent / "compass_cellload_processed"
    outdir.mkdir(parents=True, exist_ok=True)

    out_h5ad = outdir / f"{infile.stem}.cellload.filtered.h5ad"
    dup_csv = outdir / f"{infile.stem}.transcript_components.csv"
    
    if out_h5ad.exists() and out_h5ad.stat().st_mtime > infile.stat().st_mtime:
        logger.info(f"Output file already exists and is newer, skipping: {out_h5ad}")
        return 0

    try:

        with memory_monitor("load_data"):
            adata = load_subset_min_cells_only_high_memory(
                infile=infile,
                perturbation_column=perturbation_column,
                control_label=control_label,
                min_cells=min_cells,
            )

        logger.info(
            f"[debug] after load: X={'sparse' if sp.issparse(adata.X) else 'dense'}, "
            f"dtype={(adata.X.data.dtype if sp.issparse(adata.X) else np.asarray(adata.X).dtype)}"
        )

        try:
            clean_data_matrix(adata)
        except Exception as e:
            dt = (adata.X.data.dtype if sp.issparse(adata.X) else np.asarray(adata.X).dtype)
            logger.error(f"clean_data_matrix failed (X dtype={dt}): {e}")
            raise

        validate_data_integrity(adata, "after_load")

        with memory_monitor("detect_format"):
            is_log1p, frac_1e4_like = is_log1p_matrix(adata, layer=layer)
        logger.info(f"[detect_format] detected is_log1p={is_log1p}, frac_1e4_like={frac_1e4_like:.3f}")
        if is_log1p:
            logger.info("[PROCESS] 检测为log1p数据，使用反log1p分支处理")
        else:
            logger.info("[PROCESS] 检测为计数数据，使用标准化+log1p分支处理")
        def _call_filter_kd_safely(func, **all_kwargs):
            sig = inspect.signature(func)
            allowed = {k: v for k, v in all_kwargs.items() if k in sig.parameters}
            return func(**allowed)

        if is_log1p:
            with memory_monitor("process_log1p_branch"):
                kd_kwargs = dict(
                    adata=adata,
                    perturbation_column=perturbation_column,
                    control_label=control_label,
                    residual_expression=residual_expression,
                    cell_residual_expression=cell_residual_expression,
                    min_cells=min_cells,
                    layer=layer,
                    var_gene_name=gene_name_col,
                    aggregate_transcripts=aggregate_transcripts,
                    gene_name_col=gene_name_col,
                    chr_col=chr_col, start_col=start_col, end_col=end_col,
                    min_overlap_frac=min_overlap_frac,
                    reverse_log1p=True,
                    obs_gene_id_col="gene_id",
                    prefer_obs_gene_id=True,
                    log_duplicates_csv=str(dup_csv),
                    dataset_name=dataset_name,
                    cell_residual_obs_key="cell_residual_ratio",
                    cell_target_expr_obs_key="cell_target_expr",
                    cell_ctrl_mean_obs_key="cell_ctrl_mean",
                )
                adata_f = _call_filter_kd_safely(filter_on_target_knockdown, **kd_kwargs)
                del adata
                force_gc()
                if getattr(adata_f, "is_view", False):
                    logger.info("[FIX] materialize KD result (copy) to avoid sparse view assignment")
                    adata_f = adata_f.copy()                  
                if sp.issparse(adata_f.X):
                    adata_f.X = safe_matrix_conversion(adata_f.X, np.float32)
                else:
                    adata_f.X = safe_matrix_conversion(adata_f.X, np.float32)

                validate_data_integrity(adata_f, "after_filter_log1p")

                logger.info("[PROCESS] 正在执行 expm1 转换回计数数据...")
                X_counts = _expm1_inplace_adatasX(adata_f, layer=None)
                logger.info("[PROCESS] expm1 转换完成")
                adata_f.X = safe_matrix_conversion(X_counts, np.float32)

                sums = _row_sums(adata_f.X)
                if not np.allclose(sums, 1e4, rtol=0.0, atol=norm_tol_abs):
                    adata_f.X = _normalize_rows_to_target_inplace(adata_f.X, 1e4)

                clear_log1p_flag(adata_f)
                _log1p_inplace_adatasX(adata_f)
                mark_log1p(adata_f, base=None)
                mark_norm_total(adata_f, target_sum=1e4)
                record_cellload_steps(
                    adata_f,
                    input_detected_as_log1p=True,
                    post_filter_renorm_to_1e4=bool(not np.allclose(sums, 1e4, rtol=0, atol=norm_tol_abs)),
                    merged_duplicate_genes=True,
                )

        else:
            with memory_monitor("process_count_branch"):
                logger.info("Processing count data branch")

                if layer is not None:
                    if layer not in adata.layers:
                        raise KeyError(f"Layer '{layer}' not found in adata.layers")
                    X0 = adata.layers[layer]
                    try:
                        adata.X = safe_matrix_conversion(X0, np.float32)
                    except Exception as e:
                        logger.error(f"Failed to process layer '{layer}': {e}")
                        raise ValueError(f"Layer data conversion failed: {e}")
                else:
                    try:
                        adata.X = safe_matrix_conversion(adata.X, np.float32)
                    except Exception as e:
                        logger.error(f"Failed to process X matrix: {e}")
                        raise ValueError(f"X matrix conversion failed: {e}")

                validate_data_integrity(adata, "before_normalization")

                # 可选的归一化步骤
                if not skip_normalization:
                    try:
                        logger.info("[PROCESS] 正在标准化数据到10000总计数...")
                        adata.X = _normalize_rows_to_target_inplace(adata.X, 1e4)
                        logger.info("[PROCESS] 标准化完成")
                        mark_norm_total(adata, target_sum=1e4)
                    except Exception as e:
                        logger.error(f"Normalization failed: {e}")
                        raise ValueError(f"Normalization failed: {e}")
                else:
                    logger.info("[PROCESS] 跳过归一化步骤，直接使用原始count数据")
                    logger.info("[PROCESS] 注意：将为KD检测创建临时归一化副本以提高检测敏感性")

                    # 为KD检测创建临时归一化的数据副本
                    logger.info("[PROCESS] 创建临时归一化副本用于KD检测...")
                    adata_temp = adata.copy()
                    adata_temp.X = _normalize_rows_to_target_inplace(adata_temp.X, 1e4)
                    logger.info("[PROCESS] 临时归一化完成，将用于KD检测")

                validate_data_integrity(adata, "after_normalization")

                # 选择用于KD检测的数据
                kd_data = adata_temp if skip_normalization else adata

                kd_kwargs = dict(
                    adata=kd_data,
                    perturbation_column=perturbation_column,
                    control_label=control_label,
                    residual_expression=residual_expression,
                    cell_residual_expression=cell_residual_expression,
                    min_cells=min_cells,
                    layer=None,
                    var_gene_name=gene_name_col,
                    aggregate_transcripts=False,
                    gene_name_col=gene_name_col,
                    chr_col=chr_col, start_col=start_col, end_col=end_col,
                    min_overlap_frac=min_overlap_frac,
                    reverse_log1p=False,
                    obs_gene_id_col="gene_id",
                    prefer_obs_gene_id=True,
                    log_duplicates_csv=str(dup_csv),
                    dataset_name=dataset_name,
                    cell_residual_obs_key="cell_residual_ratio",
                    cell_target_expr_obs_key="cell_target_expr",
                    cell_ctrl_mean_obs_key="cell_ctrl_mean",
                    skip_normalization=skip_normalization,
                )

                try:
                    adata_f = _call_filter_kd_safely(filter_on_target_knockdown, **kd_kwargs)
                except Exception as e:
                    logger.error(f"KD filtering failed: {e}")
                    raise ValueError(f"KD filtering failed: {e}")

                # 如果跳过归一化，需要将过滤结果应用到原始数据
                if skip_normalization:
                    logger.info("[PROCESS] 将KD过滤结果应用到原始未归一化数据")
                    # 获取过滤后的细胞索引
                    filtered_obs_names = adata_f.obs_names
                    # 从原始数据中选择这些细胞
                    adata_f = adata[adata.obs_names.isin(filtered_obs_names)].copy()
                    logger.info(f"[PROCESS] 应用过滤结果：保留 {adata_f.n_obs} 个细胞")
                    # 清理临时数据
                    del adata_temp

                del adata
                force_gc()
                if getattr(adata_f, "is_view", False):
                    logger.info("[FIX] materialize KD result (copy) to avoid sparse view assignment")
                    adata_f = adata_f.copy()
                adata_f.X = safe_matrix_conversion(adata_f.X, np.float32)
                
                validate_data_integrity(adata_f, "after_filter_count")

                try:
                    logger.info("[PROCESS] 正在执行 log1p 转换...")
                    _log1p_inplace_adatasX(adata_f)
                    logger.info("[PROCESS] log1p 转换完成")
                    mark_log1p(adata_f, base=None)
                except Exception as e:
                    logger.error(f"Log1p transformation failed: {e}")
                    raise ValueError(f"Log1p transformation failed: {e}")
                    
                record_cellload_steps(
                    adata_f,
                    input_detected_as_log1p=False,
                    pre_filter_norm_to_1e4=not skip_normalization,
                    merged_duplicate_genes=True,
                    skip_normalization=skip_normalization,
                )

        with memory_monitor("standardize"):
            try:
                _standardize_names_and_control(
                    adata_f,
                    perturbation_column=perturbation_column,
                    gene_name_col=gene_name_col,
                    control_label=control_label,
                    target_ctrl_label="non-targeting",
                )
            except Exception as e:
                logger.error(f"Standardization failed: {e}")
                raise ValueError(f"Standardization failed: {e}")

        validate_data_integrity(adata_f, "final_validation")

        optimize_for_io(adata_f)
        sanitize_for_h5ad(adata_f)
        ensure_h5sparse_metadata(adata_f)

        with memory_monitor("write_output"):
            logger.info(f"[WRITE] 开始写入输出文件: {out_h5ad}")
            logger.info(f"[WRITE] 最终数据维度: {adata_f.n_obs} 细胞 × {adata_f.n_vars} 基因")
            try:
                temp_out = out_h5ad.with_suffix('.tmp.h5ad')
                adata_f.write_h5ad(temp_out, compression="lzf", 
                      compression_opts=None,
                      as_dense=())
                temp_out.rename(out_h5ad)
                logger.info(f"Successfully wrote {out_h5ad}")
            except Exception as e:
                logger.error(f"Failed to write output file: {e}")
                try:
                    temp_out = out_h5ad.with_suffix('.tmp.h5ad')
                    adata_f.write_h5ad(temp_out, compression=None)
                    temp_out.rename(out_h5ad)
                    logger.info(f"Successfully wrote {out_h5ad} (uncompressed)")
                except Exception as e2:
                    logger.error(f"Failed to write output file even without compression: {e2}")
                    raise e
            
        logger.info(f"Successfully processed {infile.name}")
        logger.info(f"Output: {out_h5ad}")
        logger.info(f"Cells kept: {adata_f.n_obs}")
        logger.info(f"Genes kept: {adata_f.n_vars}")
        
        del adata_f
        force_gc()
        
        return 0

    except KeyError as e:
        logger.error(f"Missing required column in {infile.name}: {str(e)}")
        return 1
    except ValueError as e:
        logger.error(f"Data format error in {infile.name}: {str(e)}")
        logger.error("Traceback:\n%s", traceback.format_exc())
        return 1

    except MemoryError as e:
        logger.error(f"Memory error processing {infile.name}: {str(e)}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error processing {infile.name}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        force_gc()
        return 1

def process_file_wrapper(args):
    """包装函数用于多进程处理"""
    try:
        return process_one_file_optimized(**args)
    except Exception as e:
        logger.error(f"Error in process_file_wrapper: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return 1

def main():
    p = argparse.ArgumentParser(description="Optimized KD filtering with memory management")
    group = p.add_mutually_exclusive_group(required=True)
    group.add_argument("--infile", type=str, help="Single .h5ad file to process")
    group.add_argument("--indir", type=str, help="Directory to process all *.h5ad files")

    p.add_argument("--outdir", type=str, default=None)
    p.add_argument("--n-jobs", type=int, default=1, help="Number of parallel jobs")
    p.add_argument("--max-memory-gb", type=float, default=50.0, help="Max memory per process in GB")

    p.add_argument("--perturbation-column", type=str, default="gene")
    p.add_argument("--control-label", type=str, default="non-targeting")
    p.add_argument("--residual-expression", type=float, default=0.30)
    p.add_argument("--cell-residual-expression", type=float, default=0.50)
    p.add_argument("--min-cells", type=int, default=30)
    p.add_argument("--layer", type=str, default=None)
    p.add_argument("--aggregate-transcripts", action="store_true", default=False)
    p.add_argument("--gene-name-col", type=str, default="gene_name")
    p.add_argument("--chr-col", type=str, default="chr")
    p.add_argument("--start-col", type=str, default="start")
    p.add_argument("--end-col", type=str, default="end")
    p.add_argument("--min-overlap-frac", type=float, default=0.5)
    p.add_argument("--dataset-name", type=str, default="unknown")
    p.add_argument("--norm-tol-abs", type=float, default=1.0)
    p.add_argument("--skip-normalization", action="store_true", default=False,
                   help="Skip 1e4 normalization for count data (only affects is_log1p=False branch)")

    args = p.parse_args()
    # 收集文件
    files = (sorted(Path(args.indir).glob("*.h5ad")) if args.indir
         else [Path(args.infile)])

    if not files:
        logger.error("No h5ad files found")
        return 1

    logger.info(f"Found {len(files)} files to process")

    # 准备参数
    base_kwargs = {
        'perturbation_column': args.perturbation_column,
        'control_label': args.control_label,
        'residual_expression': args.residual_expression,
        'cell_residual_expression': args.cell_residual_expression,
        'min_cells': args.min_cells,
        'layer': args.layer,
        'aggregate_transcripts': args.aggregate_transcripts,
        'gene_name_col': args.gene_name_col,
        'chr_col': args.chr_col,
        'start_col': args.start_col,
        'end_col': args.end_col,
        'min_overlap_frac': args.min_overlap_frac,
        'dataset_name': args.dataset_name,
        'norm_tol_abs': args.norm_tol_abs,
        'max_memory_gb': args.max_memory_gb,
        'skip_normalization': args.skip_normalization,
    }

    success_count = 0
    fail_count = 0
    failed_files = []

    if args.n_jobs == 1:
        # 串行处理 - 确保继续处理所有文件
        for i, file in enumerate(files, 1):
            logger.info(f"Processing file {i}/{len(files)}: {file.name}")
            kwargs = base_kwargs.copy()
            kwargs['infile'] = file
            kwargs['outdir'] = Path(args.outdir) if args.outdir else None
            
            try:
                result = process_one_file_optimized(**kwargs)
                if result == 0:
                    success_count += 1
                    logger.info(f"✓ Successfully processed {file.name}")
                else:
                    fail_count += 1
                    failed_files.append(file.name)
                    logger.error(f"✗ Failed to process {file.name}")
            except Exception as e:
                fail_count += 1
                failed_files.append(file.name)
                logger.error(f"✗ Exception processing {file.name}: {e}")
                # 继续处理下一个文件而不是退出
                continue
    else:
        logger.info(f"Using {args.n_jobs} parallel processes")

        tasks = []
        for file in files:
            kwargs = base_kwargs.copy()
            kwargs['infile'] = file
            kwargs['outdir'] = Path(args.outdir) if args.outdir else None
            tasks.append(kwargs)

        with ProcessPoolExecutor(max_workers=args.n_jobs) as executor:
            futures = {executor.submit(process_file_wrapper, task): task['infile'] for task in tasks}
            pbar = make_pbar(len(futures), desc="Files", unit="file")
            
            for future in as_completed(futures):
                file_path = futures[future]
                try:
                    result = future.result()
                    if result == 0:
                        success_count += 1
                        logger.info(f"✓ Successfully processed {file_path.name}")
                    else:
                        fail_count += 1
                        failed_files.append(file_path.name)
                        logger.error(f"✗ Failed to process {file_path.name}")
                except Exception as e:
                    fail_count += 1
                    failed_files.append(file_path.name)
                    logger.error(f"✗ Exception processing {file_path.name}: {e}")
                finally:
                    pbar.update(1)
            pbar.close()

    # 最终报告
    logger.info("=" * 60)
    logger.info("PROCESSING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total files: {len(files)}")
    logger.info(f"Successful: {success_count}")
    logger.info(f"Failed: {fail_count}")
    
    if failed_files:
        logger.info(f"Failed files:")
        for fname in failed_files:
            logger.info(f"  - {fname}")
    
    if success_count > 0:
        logger.info(f"✓ {success_count} files processed successfully")
    
    if fail_count > 0:
        logger.warning(f"⚠ {fail_count} files failed to process")
        # 不返回错误码，让脚本继续运行完毕
    else:
        logger.info("🎉 All files processed successfully!")
    
    return 0  # 总是返回0，让bash脚本继续

if __name__ == "__main__":
    raise SystemExit(main())