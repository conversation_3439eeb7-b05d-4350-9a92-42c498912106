#!/usr/bin/env python3
"""
运行数据质量校验的实际示例脚本
使用当前可用的数据文件进行比较分析
"""

import os
import sys
from pathlib import Path
import glob

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from data_quality_validation import DataQualityValidator

def find_available_data_files():
    """查找可用的数据文件"""
    
    # 定义搜索路径
    search_paths = [
        "/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/*/Seurat_object_IFNB*.h5ad",
        "/data/ioz_whr_wsx/datasets/VCC/compass_cell_load_filtered_log1p/*/Seurat_object_IFNB*.h5ad",
        "/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/*/test_skip_norm_output/Seurat_object_IFNB*.h5ad",
        "/data/ioz_whr_wsx/datasets/VCC/for_cell_load_auto/*/compass_cellload_processed/Seurat_object_IFNB*.h5ad"
    ]
    
    found_files = {}
    
    for pattern in search_paths:
        files = glob.glob(pattern)
        for file_path in files:
            file_path = Path(file_path)
            
            # 根据路径特征分类文件
            if "compass_cell_load_filtered_log1p" in str(file_path):
                category = "Standard_KD_Processing"
            elif "test_skip_norm_output" in str(file_path):
                category = "Skip_Norm_KD_Processing"
            elif "compass_cellload_processed" in str(file_path):
                category = "Regular_KD_Processing"
            elif "for_cell_load_auto" in str(file_path) and "cellload" not in file_path.name:
                category = "Original_Data"
            else:
                continue
            
            if category not in found_files:
                found_files[category] = []
            found_files[category].append(str(file_path))
    
    return found_files

def select_comparison_files(found_files):
    """选择用于比较的文件"""
    
    print("发现的数据文件:")
    for category, files in found_files.items():
        print(f"  {category}: {len(files)} 个文件")
        for file_path in files:
            file_size = Path(file_path).stat().st_size / (1024*1024)
            print(f"    {Path(file_path).name} ({file_size:.1f} MB)")
        print()
    
    # 选择比较方案
    comparison_sets = []
    
    # 方案1: 原始数据 vs 标准KD处理 vs 跳过归一化KD处理
    if ("Original_Data" in found_files and 
        "Standard_KD_Processing" in found_files and 
        "Skip_Norm_KD_Processing" in found_files):
        
        comparison_sets.append({
            "name": "Original_vs_Standard_vs_SkipNorm",
            "files": [
                found_files["Original_Data"][0],
                found_files["Standard_KD_Processing"][0],
                found_files["Skip_Norm_KD_Processing"][0]
            ],
            "labels": [
                "Original_Data",
                "Standard_KD",
                "Skip_Norm_KD"
            ]
        })
    
    # 方案2: 如果有多个KD处理结果，比较它们
    if ("Standard_KD_Processing" in found_files and 
        "Regular_KD_Processing" in found_files and
        "Skip_Norm_KD_Processing" in found_files):
        
        comparison_sets.append({
            "name": "KD_Processing_Comparison",
            "files": [
                found_files["Standard_KD_Processing"][0],
                found_files["Regular_KD_Processing"][0],
                found_files["Skip_Norm_KD_Processing"][0]
            ],
            "labels": [
                "Standard_KD",
                "Regular_KD", 
                "Skip_Norm_KD"
            ]
        })
    
    return comparison_sets

def run_validation_analysis():
    """运行校验分析"""
    
    print("=" * 60)
    print("数据质量校验分析")
    print("=" * 60)
    print()
    
    # 查找可用文件
    found_files = find_available_data_files()
    
    if not found_files:
        print("未找到可用的数据文件！")
        return
    
    # 选择比较文件
    comparison_sets = select_comparison_files(found_files)
    
    if not comparison_sets:
        print("无法找到足够的文件进行比较分析！")
        return
    
    # 运行每个比较方案
    for i, comparison in enumerate(comparison_sets):
        print(f"运行比较方案 {i+1}: {comparison['name']}")
        print("-" * 40)
        
        # 检查文件是否存在
        valid_files = []
        valid_labels = []
        
        for file_path, label in zip(comparison['files'], comparison['labels']):
            if Path(file_path).exists():
                valid_files.append(file_path)
                valid_labels.append(label)
                print(f"  ✓ {label}: {Path(file_path).name}")
            else:
                print(f"  ✗ {label}: 文件不存在 - {file_path}")
        
        if len(valid_files) < 2:
            print("  跳过此比较方案（文件不足）")
            continue
        
        # 创建输出目录
        output_dir = f"/data/ioz_whr_wsx/codes/data_preprocess/compass_cell_load/quality_validation_{comparison['name']}"
        
        try:
            # 运行校验
            validator = DataQualityValidator(valid_files, valid_labels, output_dir)
            validator.run_validation(top_perturbations=3, top_hvg=1024)
            
            print(f"✓ 比较方案 {i+1} 完成")
            print(f"  结果保存在: {output_dir}")
            
        except Exception as e:
            print(f"✗ 比较方案 {i+1} 失败: {e}")
            import traceback
            traceback.print_exc()
        
        print()

def main():
    """主函数"""
    
    # 设置环境
    os.environ["PYTHONPATH"] = str(Path(__file__).parent)
    
    try:
        run_validation_analysis()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
