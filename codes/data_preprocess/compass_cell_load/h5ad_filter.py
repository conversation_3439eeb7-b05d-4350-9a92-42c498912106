#!/usr/bin/env python3
from pathlib import Path
import sys
from contextlib import contextmanager
import anndata as ad

# —— 临时修改 sys.path 的小工具（退出后自动恢复） ——
@contextmanager
def use_local_pkg(root: str):
    sys.path.insert(0, root)
    try:
        yield
    finally:
        try:
            sys.path.remove(root)
        except ValueError:
            pass

CELL_LOAD_SRC = "/data/ioz_whr_wsx/model/code/Cell-load/src"

# 用本地源码导入，再立刻撤回 sys.path
with use_local_pkg(CELL_LOAD_SRC):
    import cell_load
    from cell_load.utils.data_utils import filter_on_target_knockdown
    # 保险起见，确认真的用了本地版本
    assert str(cell_load.__file__).startswith(CELL_LOAD_SRC), \
        f"Loaded unexpected cell_load from: {cell_load.__file__}"

# 2) 输入/输出
IN = Path("/data/vcc/process_data_vcc/h5ad/Replogle_et_al_2022/K562_gwps_normalized_singlecell_01.h5ad")
OUTDIR = IN.parent / "cellload_processed"
OUTDIR.mkdir(parents=True, exist_ok=True)
OUT = OUTDIR / f"{IN.stem}.cellload.filtered.h5ad"
DUP_CSV = OUTDIR / f"{IN.stem}.transcript_components.csv"

# 3) 读取
adata = ad.read_h5ad(IN)

# 4) KD 过滤（聚合 + log1p 反转 + obs['gene_id'] 优先）
adata_f = filter_on_target_knockdown(
    adata=adata,
    perturbation_column="gene",        # ← 如果你的扰动列是 target_gene，就改成 "target_gene"
    control_label="non-targeting",
    residual_expression=0.30,
    cell_residual_expression=0.50,
    min_cells=30,
    layer=None,                        # 如果你的 log1p 在某个 layer，比如 "log1p"，就改成那个 layer 名
    var_gene_name="gene_name",         # 仅用于定位 gene_name 列，不会改 var.index

    # 关键开关
    aggregate_transcripts=True,
    gene_name_col="gene_name",
    chr_col="chr", start_col="start", end_col="end",
    min_overlap_frac=0.5,

    reverse_log1p=True,                # 你的矩阵已 log1p：先 expm1 再列求和，再 log1p
    obs_gene_id_col="gene_id",         # 若 obs 有这个列，会优先选含该 Ensembl 的组件
    prefer_obs_gene_id=True,

    log_duplicates_csv=str(DUP_CSV),
    dataset_name="Replogle_K562",
)

# 5) 写出
adata_f.write_h5ad(OUT, compression="gzip")
print("[OK] wrote:", OUT)
print("[LOG] duplicate-transcripts CSV:", DUP_CSV)
print(f"[STAT] cells kept: {adata_f.n_obs} / {adata.n_obs}")
